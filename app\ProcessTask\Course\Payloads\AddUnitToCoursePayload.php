<?php

namespace App\ProcessTask\Course\Payloads;

use App\DTO\Courses\CourseCustomUnitDTO;
use App\DTO\Courses\CourseUnitDTO;
use App\Model\v2\Courses;
use App\Model\v2\CourseSubjects;
use App\Model\v2\CourseTemplate;
use App\Model\v2\SubjectUnits;
use App\Model\v2\Units;

class AddUnitToCoursePayload
{
    public function __construct(
        /* the course unit is being added to */
        public Courses $course,
        /* The template the unit & subject is being added to */
        public CourseTemplate|int|null $template = null,
        /* unit that is to be added */
        public CourseUnitDTO|CourseCustomUnitDTO|null $unitToSave = null,
        /* the record of master unit table if no record found a new has to be created */
        public ?Units $masterUnit = null,
        /* the subject the unit should be assigned to */
        public ?CourseSubjects $subject = null,
        /* the unit data should be assigned to the subject */
        public ?SubjectUnits $subjectUnit = null,
    ) {}
}
