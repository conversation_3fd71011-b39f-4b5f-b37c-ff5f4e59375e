<?php

namespace App\ProcessTask\Course;

use App\Model\v2\CourseSubjects;
use App\Model\v2\Subject;
use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;
use Closure;

class FindAndUpdateSubject implements SubjectMethodsInterface
{
    use SubjectAddUpdateMethods;

    public function handle(AddUnitToCoursePayload $payload, Closure $next): AddUnitToCoursePayload
    {
        /* get the course subject for the */
        $courseSubjectId = $payload->subjectUnit->course_subject_id ?? null;
        $courseSubject = null;
        if ($courseSubjectId) {
            $courseSubject = CourseSubjects::where(['course_id' => $payload->course->id, 'id' => $courseSubjectId])->first();
        }
        if (empty($courseSubject)) {
            $this->addSubjectAndUnit($payload, true);
            $courseSubject = $payload->subject;
        }

        $subjectData = $payload->unitToSave ?? null;

        $updateValues = [
            'subject_code' => $subjectData->subject_code ?? '',
            'subject_name' => $subjectData->subject_name ?? '',
            'subject_type' => $subjectData->subject_type ?? '',
            'grading_type' => $subjectData->grading_type ?? '',
            'max_marks_allow' => $subjectData->max_marks_allow ?? '',
            'contact_hours' => $subjectData->contact_hours ?? '',
            'level' => $subjectData->level ?? null,
            'credit_point' => $subjectData->credit_point ?? '',
            'is_long_indicator' => $subjectData->is_long_indicator ?? 0,
            'is_assessment' => $subjectData->is_assessment ?? 0,
            'discipline_broad_type' => $subjectData->discipline_broad_type ?? '',
            'discipline_narrow_type' => $subjectData->discipline_narrow_type ?? '',
            'discipline_narrow_sub_type' => $subjectData->discipline_narrow_sub_type ?? '',
            'course_stage' => $subjectData->course_stage ?? '',
            'subject_fee' => $subjectData->subject_fee ?? '',
            'domestic_subject_fee' => $subjectData->domestic_subject_fee ?? '',
            'delivery_mode' => $subjectData->delivery_mode ?? '',
            'internal' => $subjectData->internal ?? '',
            'external' => $subjectData->external ?? '',
            'workplace_based_delivery' => $subjectData->workplace_based_delivery ?? '',
            'EFTSL_study_load' => $subjectData->EFTSL_study_load ?? '',
            'is_active' => $subjectData->is_active ?? '',
            'inc_in_certificate' => $subjectData->inc_in_certificate ?? '',
        ];

        $courseSubject->update($updateValues);
        $payload->subject = $courseSubject;

        return $next($payload);
    }
}
