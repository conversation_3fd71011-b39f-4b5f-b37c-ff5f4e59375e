<?php

namespace App\Services\ResultImport;

use App\Model\v2\AssessmentTask;
use App\Model\v2\AssignedAssessmentTask;
use App\Model\v2\CourseType;
use App\Model\v2\ResultGrade;
use App\Model\v2\Semester;
use App\Model\v2\StudentAssignedAssessmentTask;
use App\Model\v2\Subject;
use Illuminate\Support\Facades\Log;

class ResultCreator
{
    /**
     * Create a result record from the import data
     *
     * @param  \App\Model\v2\ResultImport  $importRecord
     * @param  array  $related
     * @return array
     */
    public function create($importRecord, $related)
    {
        Log::info('Creating Result: '.$importRecord);
        $importData = $importRecord->import_data;

        $findStudentId = $related['student'];
        $findSubjectId = $related['unit'];
        $findStudentSubjectEnrollmentInfo = $related['enrollment'];

        Log::info('Import Data: '.json_encode($importData));
        $needToUpdateFinalResult = false;
        $messages = [];

        $courseType = CourseType::query()->where('title', $importData['CourseType'])->first();
        $courseTypeId = $courseType->id ?? 0;

        $isHigherEd = (new CourseType)->checkHigherEdGradingType($courseTypeId, $findStudentSubjectEnrollmentInfo['college_id']);

        Log::info('Is Higher Ed: '.$isHigherEd);

        $i = 0;
        foreach ($importData['assessmentData'] as $key => $value) {
            $i++;

            Log::info('Assessment Processing: '.$key.' => '.$value);

            $assessmentTaskInfo = AssessmentTask::where('task_name', trim($key))->where('subject_id', $findSubjectId['subject_id'])->first();

            if ($assessmentTaskInfo) {
                Log::info('Assessment Found: '.$key.' => '.$value);
                $assignedAssessmentTaskInfo = AssignedAssessmentTask::where([
                    'assessment_task_id' => $assessmentTaskInfo->id,
                    'batch' => trim($findStudentSubjectEnrollmentInfo['batch']),
                ])->first();

                if ($assignedAssessmentTaskInfo) {
                    Log::info('Assessment Assigned: '.$key.' => '.$value);
                    $findStudentAssignedAssessmentTaskInfo = StudentAssignedAssessmentTask::where([
                        'student_id' => $findStudentId['id'],
                        'assessment_task_id' => $assessmentTaskInfo->id,
                        'subject_id' => $findSubjectId['subject_id'],
                    ])->first();

                    if ($findStudentAssignedAssessmentTaskInfo) {

                        Log::info('Assessment Found for Student: '.$key.' => '.$value);
                        if ($isHigherEd) {
                            $findStudentAssignedAssessmentTaskInfo->update([
                                'marks' => is_numeric($value) ? $value : 0,
                            ]);
                            Log::info('Assessment Updated: '.$key.' => '.$value);
                        }

                        $messages['success'][] = $key.' Task update for student '.$importData['StudentId'];
                    } else {

                        Log::info('Assessment Not Found for Student: '.$key.' => '.$value);

                        Log::info('Assessment Start Creating: '.$key.' => '.$value);

                        $semesterInfo = Semester::where('id', $findStudentSubjectEnrollmentInfo['semester_id'])->first();
                        StudentAssignedAssessmentTask::create([
                            'college_id' => $findStudentSubjectEnrollmentInfo['college_id'],
                            'course_type_id' => $courseTypeId,
                            'student_id' => $findStudentId['id'],
                            'year' => $semesterInfo->year,
                            'semester_id' => $findStudentSubjectEnrollmentInfo['semester_id'],
                            'term' => $findStudentSubjectEnrollmentInfo['term'],
                            'batch' => $findStudentSubjectEnrollmentInfo['batch'],
                            'course_id' => $findStudentSubjectEnrollmentInfo['course_id'],
                            'assessment_task_id' => $assessmentTaskInfo->id,
                            'subject_attempts' => 1,
                            'marks' => ($isHigherEd) ? (is_numeric($value) ? $value : 0) : null,
                            'competency' => (! $isHigherEd) ? $value : null,
                            'subject_id' => $findSubjectId['subject_id'],
                            'created_by' => $importRecord->created_by,
                            'updated_by' => $importRecord->created_by,
                        ]);

                        $messages['success'][] = $key.' Task created for student '.$importData['StudentId'].' batch '.$findStudentSubjectEnrollmentInfo['batch'];
                    }
                } else {
                    Log::info($key.' task is not assigned to batch '.$findStudentSubjectEnrollmentInfo['batch'].' for student '.$importData['StudentId']);
                }
            } else {
                Log::info($key.' task is Not Found.');
            }

            if (count($importData['assessmentData']) == $i) {
                $needToUpdateFinalResult = true;
            }
        }

        if ($needToUpdateFinalResult) {
            Log::info('Updating Final Result: '.$importData['StudentId']);
            if ($isHigherEd) {
                $arrSubject = (new Subject)->getSubjectlistCollegeIdWise($findSubjectId['subject_id']);
                $objResultGrade = ResultGrade::getGradFromMarks($findStudentSubjectEnrollmentInfo['college_id'], $arrSubject[0]['grading_type'], $importData['TotalMark']);
                $importData['Grade'] = $objResultGrade['grade'] ?? '';
                $markOutcome = $objResultGrade['mark_outcome'] ?? null;
            } else {
                $markOutcome = null;
            }

            $updateData = [
                'marks' => trim($importData['TotalMark']),
                'grade' => trim($importData['Grade'] ?? ''),
            ];

            if ($markOutcome !== null) {
                $updateData['mark_outcome'] = $markOutcome;
            }

            $findStudentSubjectEnrollmentInfo->update($updateData);

            $messages['success'][] = $importData['StudentId'].' Update successfully.';
        }

        return $messages;
    }
}
