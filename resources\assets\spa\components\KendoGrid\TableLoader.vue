<template lang="">
    <div :class="rootClass">
        <div class="k-loader-container-overlay bg-white/80" />
        <div class="k-loader-container-inner">
            <Loader :showText="false" :size="24" />
        </div>
    </div>
</template>
<script>
import { twMerge } from 'tailwind-merge';
import Loader from '../Loader/Spinner.vue';
export default {
    props: {
        pt: {
            type: Object,
            default: {},
        },
    },
    components: {
        Loader,
    },
    computed: {
        rootClass() {
            return twMerge(
                'k-loader-container k-loader-container-md k-loader-top flex justify-center items-center min-h-64',
                this.pt.root
            );
        },
        overlayClass() {
            return twMerge('k-loader-container-overlay bg-white', this.pt.overlay);
        },
    },
};
</script>
<style lang=""></style>
