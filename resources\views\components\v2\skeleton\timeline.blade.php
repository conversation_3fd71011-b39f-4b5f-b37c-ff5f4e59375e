@props([
'items' => 2
])

<div {{$attributes->twMerge("tw-timeline-skeleton space-y-4")}}>
    <div class="h-32 flex items-end gap-8">
        <div class="w-8 h-8 rounded-full bg-gray-200 animate-pulse">
        </div>
        <div class="flex items-end justify-between gap-10">
            @for($i = 0; $i < $items; $i++) <div class="flex flex-col items-center justify-center gap-1">
                <span class="h-4 w-24 animate-pulse bg-gray-200"></span>
                <span class="h-4 w-28 animate-pulse bg-gray-200"></span>
                <span class="h-4 w-24 animate-pulse bg-gray-200"></span>
                <div class="w-8 h-8 rounded-full bg-gray-200 animate-pulse">
                </div>
        </div>
        @endfor
    </div>
    <div class="w-8 h-8 rounded-full bg-gray-200 animate-pulse ml-auto">
    </div>
</div>
<div class="p-4 space-y-2 rounded border border-gray-100">
    <div class="flex space-x-3 items-center animate-pulse">
        <div class="w-2 h-6 bg-gray-200 rounded"></div>
        <span class="w-1/2 h-6 bg-gray-200 rounded"></span>
    </div>
    <div class="grid grid-cols-4 gap-2">
        <div class="flex flex-col justify-start items-start animate-pulse">
            <span class="w-1/3 h-4 bg-gray-200 rounded"></span>
            <span class="w-2/3 h-4 bg-gray-200 rounded"></span>
        </div>
        <div class="flex flex-col justify-start items-start animate-pulse">
            <span class="w-1/3 h-4 bg-gray-200 rounded"></span>
            <span class="w-2/3 h-4 bg-gray-200 rounded"></span>
        </div>
        <div class="flex flex-col justify-start items-start animate-pulse">
            <span class="w-1/3 h-4 bg-gray-200 rounded"></span>
            <span class="w-2/3 h-4 bg-gray-200 rounded"></span>
        </div>
        <div class="flex flex-col justify-start items-start animate-pulse">
            <span class="w-1/3 h-4 bg-gray-200 rounded"></span>
            <span class="w-2/3 h-4 bg-gray-200 rounded"></span>
        </div>
    </div>
    <span class="h-4 bg-gray-200 rounded animate-pulse"></span>
</div>
</div>