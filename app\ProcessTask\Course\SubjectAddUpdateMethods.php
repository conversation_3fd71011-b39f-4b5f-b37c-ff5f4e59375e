<?php

namespace App\ProcessTask\Course;

use App\Classes\SiteConstants;
use App\Model\v2\CourseSubjects;
use App\Model\v2\Subject;
use App\Model\v2\SubjectUnits;
use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;

trait SubjectAddUpdateMethods
{
    public function addSubjectAndUnit(AddUnitToCoursePayload $payload, $onlySubject = false)
    {

        $subjectType = $payload->unitToSave->Type ?? null;
        $masterUnitType = $payload->masterUnit->unit_type ?? null;
        $subjectType = $subjectType ?? $masterUnitType ?? SiteConstants::ELECTIVETEXT;
        $subjectType = $subjectType == SiteConstants::CORETEXT ? SiteConstants::CORETEXT : SiteConstants::ELECTIVETEXT;
        $unitData = $payload->masterUnit ?? $payload->subjectUnit ?? null;

        $subjectData = [
            'college_id' => $payload->course->college_id ?? null,
            'course_id' => $payload->course->id ?? null,
            'subject_code' => $unitData->unit_code ?? null,
            'subject_name' => $unitData->unit_name ?? null,
            'subject_type' => $subjectType,
            'contact_hours' => $unitData->nominal_hours ?? null,
            'subject_fee' => $unitData->tution_fees ?? null,
            'domestic_subject_fee' => $unitData->domestic_tution_fees ?? null,
            'delivery_mode' => $unitData->delivery_mode ?? null,
            'internal' => $unitData->internal ?? null,
            'external' => $unitData->external ?? null,
            'workplace_based_delivery' => $unitData->workplace_based_delivery ?? null,
            'EFTSL_study_load' => $unitData->EFTSL_study_load ?? null,
            'is_active' => $unitData->status ?? null,
        ];
        /* get the master subject */
        $masterSubject = Subject::where('subject_code', $subjectData['subject_code'])->first();
        if (! $masterSubject) {
            $masterSubject = new Subject($subjectData);
            $masterSubject->save();
        }
        $subjectData['synced_subject'] = $masterSubject->id;
        /* check if the course has subject */
        $subject = CourseSubjects::where([
            'course_id' => $subjectData['course_id'],
            'synced_subject' => $subjectData['synced_subject'],
        ])->first();
        if (! $subject) {
            $subject = new CourseSubjects($subjectData);
            $subject->saveQuietly();
        }
        $payload->subject = $subject;
        if ($onlySubject) {
            return $payload;
        }
        /* now add unit to the subject */
        $unitData = $unitData->toArray();

        $unitData['course_subject_id'] = $subject->id;
        $unitData['course_id'] = $payload->course->id;
        $unitData['unit_type'] = $subjectType;
        $unitData['unit_id'] = $unitData['id'];
        /* find subject unit */
        if ($payload->subjectUnit) {
            $subjectUnit = $payload->subjectUnit;
        } else {
            $subjectUnit = $subject->subject_units()->where([
                'unit_id' => $unitData['unit_id'],
            ])->first();
            $payload->subjectUnit = $subjectUnit;
        }
        if (! $subjectUnit) {
            $subjectUnit = new SubjectUnits($unitData);
            $subjectUnit->saveQuietly();
            $payload->subjectUnit = $subjectUnit;
        } elseif ($subjectUnit->course_subject_id != $subject->id) {
            $subjectUnit->updateQuietly($unitData);
        }

        return $payload;
    }

    public function removeSubjectAndUnit(AddUnitToCoursePayload $payload)
    {
        $subject = CourseSubjects::with(
            [
                'subject_unit' => function ($query) use ($payload) {
                    $query->where(['unit_id' => $$payload->masterUnit->id]);
                },
            ]
        )->where([
            'course_id' => $payload->course->id,
            'subject_code' => $payload->masterUnit->unit_code,
        ])->first();
        if ($subject) {
            $subjectUnit = $subject->subject_unit ?? null;

            $payload->subject = $subject;
            $payload->subjectUnit = $subjectUnit;

            foreach ($subjectUnit as $unit) {
                $unit->deleteQuietly();
            }
            $subject->deleteQuietly();
        }

        return $payload;
    }
}
