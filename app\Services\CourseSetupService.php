<?php

namespace App\Services;

use App\Classes\CoursesApiController;
use App\Classes\SiteConstants;
use App\DTO\Courses\ApiResponseCourseDTO;
use App\DTO\Courses\CompetencyCriteriaDTO;
use App\DTO\Courses\IntakeDataDTO;
use App\DTO\Courses\IntakesFilterDTO;
use App\Exceptions\ApplicationException;
use App\Http\Resources\CourseAvetmissResource;
use App\Http\Resources\CourseFacultyResource;
use App\Http\Resources\CourseFeeResource;
use App\Http\Resources\CourseGeneralResource;
use App\Http\Resources\CourseShortCourseIntakesResource;
use App\Http\Resources\CourseTemplatesResource;
use App\Http\Resources\HigheredCourseResource;
use App\Http\Resources\TemplateUnitsCopyResource;
use App\Model\v2\CampusIntake;
use App\Model\v2\CollegeCampus;
use App\Model\v2\CollegeCourse;
use App\Model\v2\Courses;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\CourseSubject;
use App\Model\v2\CourseSubjects;
use App\Model\v2\CourseTemplate;
use App\Model\v2\CourseTemplateStructure;
use App\Model\v2\CourseType;
use App\Model\v2\ElementOfUnitCompetency;
use App\Model\v2\HigheredCourseDetail;
use App\Model\v2\StudentCourses;
use App\Model\v2\Subject;
use App\Model\v2\SubjectUnits;
use App\Model\v2\Units;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class CourseSetupService
{
    protected $courseunits;

    public function __construct()
    {
        $this->courseunits = new CourseUnitsService;
    }

    public function hasNewCourseSetup()
    {
        $currentTenant = tenant()->id;
        $newCourseSetup = config("features.tenants.{$currentTenant}.newcourse", false);
        $newCourseSetup = true; // for now set all to true

        return $newCourseSetup;
    }

    public function deleteCourseTemplate($id = 0)
    {
        $college_id = Auth::user()->college_id;
        DB::beginTransaction();
        try {
            $res = Courses::where(['id' => $id, 'college_id' => $college_id])->first();
            if (! $res) {
                $msg = config_locale('messages.courses.deletenotfound', 'Errors!!!');
                throw new ApplicationException($msg);
            }
            // if course found delete all the related data
            // college_id, course_id
            $objStudentCourse = new StudentCourses;
            $arrStudentCourse = $objStudentCourse->_checkCourseExist($res->id);
            $objCourseSubject = new CourseSubject;
            $arrCourseSubject = $objCourseSubject->_checkCourseExist($res->id);
            if ($arrStudentCourse > 0 || $arrCourseSubject > 0) {
                throw new ApplicationException(config_locale('messages.courses.deletenotallowed', 'Errors!!!'));
            }
            /*
            as course is using softdelete
            units and other dependent records are not being deleted
            */
            $res->delete();
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            // safeDD($e);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getProgressStatus(Courses $course)
    {
        $vetCourseTypeId = config('constants.vet_course_type_id');
        $shortCourseTypeId = config('constants.short_course_type_id');
        $higherEdCourseTypeId = config('constants.highered_course_type_id');
        // check if general fields are okay
        $checkFields = ['course_type_id', 'course_code', 'course_name', 'delivery_target'];
        if ($course['course_type_id'] != 2) {
            $checkFields[] = 'national_code';
        }
        if (! empty($course['delivery_target']) && strtolower($course['delivery_target']) != 'domestic' && ! $course['is_cricos_code']) {
            $checkFields[] = 'cricos_code';
        }
        $generalOkay = $this->checkFields($course, $checkFields, 'General');
        // check if all hour and fees fileds are filled
        $checkFields = ['course_duration', 'couse_duration_type', 'maximum_weekly_study'];
        if (strtolower($course['delivery_target']) == 'international') {
            $checkFields = [...$checkFields, ...['tuition_fee']];
        } elseif (strtolower($course['delivery_target']) == 'domestic') {
            $checkFields = [...$checkFields, ...['domestic_fee']];
        } else {
            $checkFields = [...$checkFields, ...['domestic_fee', 'tuition_fee']];
        }
        $feesOkay = $this->checkFields($course, $checkFields, 'Hours & Fees');
        // check if all faculties and campuses fileds are filled
        $checkFields = ['campus_list', 'results_calculation_methods'];
        $facultiesOkay = $this->checkFields($course, $checkFields, 'Faculties & Campus');
        // check of all avetmiss filelds are filled
        $checkFields = ['course_recognition_id', 'level_of_education_id', 'field_of_education_id', 'ANZSCO_code'];
        if (! in_array($course['course_type_id'], [$vetCourseTypeId, $shortCourseTypeId])) {
            $checkFields = [];
        }
        $avetmissOkay = $this->checkFields($course, $checkFields, 'Avetmiss');
        // check if units and subjects are filled as per required on template
        $unitsOkay = $this->getUnitsProgressStatus($course);
        // check if higered fields are filled
        if ($course['course_type_id'] == $higherEdCourseTypeId) {
            $higheredOkay = $this->getHigheredProgressStatus($course['id']);
        } else {
            $higheredOkay = ['status' => true, 'fields' => []];
        }
        if ($course['course_type_id'] == $shortCourseTypeId) {
            $intakesOkay = $this->getShortCourseIntakesProgressStatus($course['id']);
        } else {
            $intakesOkay = ['status' => true, 'fields' => []];
        }
        $result = [
            'general' => $generalOkay,
            'fees' => $feesOkay,
            'faculties' => $facultiesOkay,
            'avetmiss' => $avetmissOkay,
            'units' => $unitsOkay,
            'intakes' => $intakesOkay,
            'info' => $higheredOkay,
        ];

        return $result;
    }

    public function getHigheredProgressStatus($course_id)
    {
        $higherEd = HigheredCourseDetail::Where('course_id', $course_id)->first();
        $checkFields = ['course_fee_type', 'student_contribution_fee', 'study_type', 'study_load'];

        return $this->checkFields($higherEd, $checkFields, 'More Information');
    }

    public function getShortCourseIntakesProgressStatus($course_id)
    {
        $intakes = CoursesIntakeDate::where('course_id', $course_id)->count();

        return ['status' => $intakes > 0, 'fields' => ($intakes > 0) ? [] : ['Intakes are not set for this course.']];
    }

    public function getUnitsProgressStatus($course)
    {
        /*
        this query makes the unit_type of units table more dominant
        might be different case in other course types
        */
        // if the course has templates check if all the templates fulfill the package requirements
        $coreText = SiteConstants::CORETEXT;
        $electiveText = SiteConstants::ELECTIVETEXT;
        $templatesList = CourseTemplate::leftJoin('rto_course_template_structure', 'rto_course_template_structure.course_template_id', '=', 'rto_course_template.id')
            ->where('rto_course_template.course_id', $course['id'])
            ->where('rto_course_template.set_active', 1)
            ->select(
                'rto_course_template.id',
                'rto_course_template.template_name',
                'rto_course_template.no_of_core_subject',
                'rto_course_template.no_of_elective_subject',
                DB::raw('SUM(rto_course_template_structure.unit_type = "'.$coreText.'") AS Core'),
                DB::raw('SUM(rto_course_template_structure.unit_type = "'.$electiveText.'") AS Elective'),
                DB::raw('CASE when (
                                            SUM(rto_course_template_structure.unit_type = "'.$coreText.'") = rto_course_template.no_of_core_subject and
                                            SUM(rto_course_template_structure.unit_type = "'.$electiveText.'") = rto_course_template.no_of_elective_subject
                                        ) THEN 1 ELSE 0 END AS satisfied')
            )
            ->groupBy('rto_course_template.id')
            ->get();

        $templatesCount = $templatesList->count();
        $unitsOkay = true;
        // $templatesCount = 0;
        if ($templatesCount > 0) {
            $message = null;

            foreach ($templatesList as $template) {
                if ($template->satisfied != 1) {
                    $unitsOkay = false;
                    if ($message === null) {
                        $message = [];
                    }
                    // get the full message
                    $infoMsg = [];
                    if ($template->no_of_core_subject > $template->Core) {
                        $diff = $template->no_of_core_subject - $template->Core;
                        $infoMsg[] = "Add {$diff} more {$coreText} units.";
                    } elseif ($template->no_of_core_subject < $template->Core) {
                        $diff = $template->Core - $template->no_of_core_subject;
                        $infoMsg[] = "Remove {$diff} {$coreText} units";
                    }
                    if ($template->no_of_elective_subject > $template->Elective) {
                        $diff = $template->no_of_elective_subject - $template->Elective;
                        $infoMsg[] = "Add {$diff} more {$electiveText} units.";
                    } elseif ($template->no_of_elective_subject < $template->Elective) {
                        $diff = $template->Elective - $template->no_of_elective_subject;
                        $infoMsg[] = "Remove {$diff} {$electiveText} units";
                    }
                    $infoMsg = implode(' and ', $infoMsg);
                    $message[] = trim("{$template->template_name} does not fulfill the package requirement. {$infoMsg}");
                }
            }
        } else {
            // the course has no templates; so check the master template
            /* old setup query */
            /*
            $unitsCount = CourseSubject::join('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_course_subject.subject_id')
                        ->where('rto_course_subject.course_id', $course["id"])
                        ->whereIn('rto_subject_unit.unit_type', [SiteConstants::CORETEXT, SiteConstants::ELECTIVETEXT])
                        ->selectRaw('SUM(rto_subject_unit.unit_type = "'.SiteConstants::CORETEXT.'") AS Core, SUM(rto_subject_unit.unit_type = "'.SiteConstants::ELECTIVETEXT.'") AS Elective')
                        ->first();
            */
            /* new setup query */
            $unitsCount = SubjectUnits::where('rto_subject_units.course_id', $course['id'])
                ->where('rto_subject_units.status', 1)
                ->whereIn('rto_subject_units.unit_type', [SiteConstants::CORETEXT, SiteConstants::ELECTIVETEXT])
                ->selectRaw('
                                        SUM(rto_subject_units.unit_type = "'.SiteConstants::CORETEXT.'") AS Core, 
                                        SUM(rto_subject_units.unit_type = "'.SiteConstants::ELECTIVETEXT.'") AS Elective
                                    ')
                ->first();
            // dd($unitsCount);
            $totalCore = $unitsCount[SiteConstants::CORETEXT] ?? 0;
            $totalElective = $unitsCount[SiteConstants::ELECTIVETEXT] ?? 0;
            // dd($course["core_units_number"],$course["elective_units_number"]);
            $coreMax = $course['core_units_number'] ?? 0;
            $electiveMax = $course['elective_units_number'] ?? 0;

            $unitsOkay = true;
            if (! $course['core_units_number'] || $totalCore != $coreMax) {
                $unitsOkay = false;
            }
            if ($totalElective < $electiveMax) {
                $unitsOkay = false;
            }
            $message = (! $unitsOkay) ? ['Units count does not fulfill the package requirements'] : null;
        }
        if ($unitsOkay) {
            // check if all the units are assigned to subjects
            $orphanUnits = SubjectUnits::where('course_id', $course['id'])->where('status', 1)->where('course_subject_id', null)->count();
            if ($orphanUnits) {
                $unitsOkay = false;
                $extrainfo = ($templatesCount > 0) ? ' Check the '.SiteConstants::MASTER_TEMPLATE_NAME.' for details.' : '';
                $message = ["There are {$orphanUnits} units not assigned to any subject.{$extrainfo}"];
            }
            // select * from rto_subject_units where course_id = 69;

        }

        return ['status' => $unitsOkay, 'fields' => $message];
    }

    private function checkFields($dataArray, $keys, $tabName = null)
    {
        $hasEmptyValue = false;
        $incompleteFields = [];
        foreach ($keys as $key) {
            // Check if the key is set and not empty
            if (! isset($dataArray[$key]) || empty($dataArray[$key])) {
                $hasEmptyValue = true;
                $key = ucwords(str_replace('_', ' ', $key));
                $tabNameText = ($tabName) ? "{$tabName}:: " : '';
                $incompleteFields[] = "{$tabNameText}{$key} value is missing.";
            }
        }

        return ['status' => ! $hasEmptyValue, 'fields' => $incompleteFields];
    }

    public function sortUnits($courseId = 0, $idsQueue = [])
    {
        DB::beginTransaction();
        try {
            // get course
            $course = Courses::find($courseId);
            if (! $course) {
                throw new ApplicationException(config_locale('messages.courses.nocourse'));
            }
            // Get distinct values
            $unique_map_ids = (! empty($idsQueue) && is_array($idsQueue)) ? array_unique($idsQueue) : [];
            $updates = [];
            $ordercnt = 1;
            $updatedCnt = 0;
            $order = [];
            foreach ($unique_map_ids as $index => $value) {
                $order[$value] = $ordercnt;
                $updated = CourseSubject::where(['id' => $value, 'course_id' => $courseId])->update(['display_order' => $ordercnt]);
                if ($updated) {
                    $updatedCnt++;
                }
                $ordercnt++;
            }
            DB::commit();

            return $updatedCnt;
        } catch (\Exception $e) {
            DB::rollBack();
            // return ajaxError($e->getMessage(), 500);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function bulkAddDates($params = [])
    {
        $college_id = Auth::user()->college_id;
        DB::beginTransaction();
        try {
            // get all the intake dates
            $id = (int) $params['id'] ?? 0;
            $intakeCampuses = array_filter($params['intakes_for'], function ($item) {
                return $item['checked'];
            });
            $intakeCampuses = ! empty($intakeCampuses) ? array_column($intakeCampuses, 'id') : [];
            // verifies if the campuses are available for the college
            $intakeCampuses = CollegeCampus::select('id')->where('college_id', $college_id)
                ->whereIn('id', $intakeCampuses)->pluck('id')->toArray();
            $intake_dates = $params['intake_dates'] ?? []; // will contain name and date and intakes_for (not required)
            $selectedCourses = array_filter($params['courses'], function ($item) {
                return $item['checked'];
            });
            $selectedCourses = ! empty($selectedCourses) ? array_column($selectedCourses, 'id') : [];
            $selectedYears = array_filter($params['years'], function ($item) {
                return $item['checked'];
            });
            $intakeStatus = ($params['publish'] == true) ? 1 : 0;
            $selectedYears = ! empty($selectedYears) ? array_column($selectedYears, 'id') : [];
            // now create the course intake date
            // loop through all the courses
            // loop through all the dates
            // for
            $courses = Courses::where('college_id', $college_id)
                ->whereIn('id', $selectedCourses)->get();
            $allIntakes = $allIntakesMapped = [];
            $totalUpdated = $totalCreated = 0;
            foreach ($courses as $course) {
                $intakeDateData = [
                    'college_id' => $college_id,
                    'course_type' => $course->course_type_id,
                    'course_id' => $course->id,
                    'intake_year' => null,
                    'intake_name' => null,
                    'intake_start' => null,
                    'intake_end' => null,
                    'intake_duration' => null,
                    'intake_receiver' => 'Both',
                    'active' => $intakeStatus,
                    'batch_number' => time(),
                ];
                // for every time selected
                foreach ($intake_dates as $intake) {
                    $intakeDate = $intake['date'] ?? null;
                    $intakeName = $intake['name'] ?? null;
                    if (empty($intakeDate)) {
                        continue;
                    }
                    $intakeDate = Carbon::parse($intakeDate);
                    $intakeYear = $intakeDate->year;
                    $intakeMonth = $intakeDate->format('m');
                    $intakeDay = $intakeDate->day;
                    $intake_receiver = in_array(strtolower($intake['receiver']), ['international', 'local', 'both']) ? ucfirst($intake['receiver']) : 'Both';
                    if (empty($selectedYears)) {
                        $selectedYears[] = $intakeYear;
                    }
                    foreach ($selectedYears as $year) {
                        $newDate = $year.'-'.$intakeMonth.'-'.$intakeDay;
                        $selIntakeDate = Carbon::parse($newDate);
                        $intakeYear = $selIntakeDate->year;
                        $intakeMonthName = $selIntakeDate->format('F');
                        if (empty($intakeName)) {
                            $intakeName = $intakeMonthName.' Intake';
                        }
                        $intakeEnd = $this->getEndDate($selIntakeDate, $course->course_duration, $course->couse_duration_type);
                        $selIntakeDate = $selIntakeDate->format('Y-m-d');
                        $durationDays = '';
                        $intakeDateData['intake_year'] = $intakeYear;
                        $intakeDateData['intake_name'] = $intakeName;
                        $intakeDateData['intake_start'] = $selIntakeDate;
                        $intakeDateData['intake_end'] = $intakeEnd;
                        $intakeDateData['intake_duration'] = $course->course_duration;
                        $intakeDateData['batch_number'] = $intakeYear;
                        $intakeDateData['intake_receiver'] = $intake_receiver;
                        $filtrData = Arr::only($intakeDateData, ['college_id', 'course_id', 'intake_start']);
                        $saveData = Arr::only($intakeDateData, ['course_type', 'intake_year', 'intake_name', 'intake_start', 'intake_end', 'intake_duration', 'intake_receiver', 'active', 'batch_number']);
                        // dump($filtrData);
                        // dd($saveData);
                        $updatedIntakes = CoursesIntakeDate::updateOrCreate(
                            $filtrData, $saveData
                        );
                        if ($updatedIntakes) {
                            if ($updatedIntakes->wasRecentlyCreated) {
                                $totalCreated++;
                            } else {
                                $totalUpdated++;
                            }
                            $intakeMapArray = [
                                ['intake_id' => $updatedIntakes->id, 'campus_id' => 0],
                            ];
                            $intakesMap = collect($intakeMapArray)
                                ->crossJoin($intakeCampuses)
                                ->map(function ($item) use ($intakeStatus) {
                                    return ['intake_id' => $item[0]['intake_id'], 'campus_id' => $item[1], 'status' => $intakeStatus];
                                })
                                ->toArray();
                            $allIntakesMapped = [...$allIntakesMapped, ...$intakesMap];
                            $allIntakes[] = $updatedIntakes;
                        }
                    }
                }
            }
            if (! empty($allIntakesMapped)) {
                foreach ($allIntakesMapped as $intake) {
                    $condition = [
                        'intake_id' => $intake['intake_id'],
                        'campus_id' => $intake['campus_id'],
                    ];

                    $values = [
                        'status' => $intake['status'],
                    ];
                    CampusIntake::updateOrCreate($condition, $values);
                }
            }
            DB::commit();

            return ['total' => ($totalCreated + $totalUpdated), 'inserted' => $totalCreated, 'updated' => $totalUpdated];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getEndDate(Carbon $date, $duration = 0, $durationType = 1)
    {
        $endDate = $date->copy();
        if ($durationType == 2) { // week
            $endDate = $endDate->addWeeks($duration);
        } elseif ($durationType == 3) { // month
            $endDate = $endDate->addMonths($duration);
        } elseif ($durationType == 4) { // year
            $endDate = $endDate->addYears($duration);
        } else { // days
            $endDate = $endDate->addDays($duration);
        }

        return ($endDate) ? $endDate->format('Y-m-d') : null;
    }

    public function getIntakesData(IntakesFilterDTO $filterArray, $courseId = null)
    {
        $coursesQuery = Courses::With([
            'campuses' => function ($query) {
                $query->whereHas('campus', function ($subQuery) {
                    $subQuery->where('status', 1);
                });
            },
            'campuses.campus',
            'coursetype',
            'intakes' => function ($query) use ($filterArray) {
                $query->whereHas('campusIntakes', function ($subQuery) use ($filterArray) {
                    if ($filterArray->campus) {
                        $subQuery->where('campus_id', $filterArray->campus);
                    }
                    if ($filterArray->years) {
                        $subQuery->whereIn('intake_year', $filterArray->years);
                    }
                });
            },
            'intakes.campusIntakes' => function ($query) use ($filterArray) {
                $query->with('campus');
                if ($filterArray->campus) {
                    $query->where('campus_id', $filterArray->campus);
                }
                if ($filterArray->years) {
                    // $query->whereIn('intake_year', $filterArray->years);
                }
            },
        ])
            ->select('rto_courses.*')
            ->join('rto_course_intake_dates', 'rto_courses.id', '=', 'rto_course_intake_dates.course_id')
            ->join('rto_course_campus', 'rto_courses.id', '=', 'rto_course_campus.course_id')
            ->where('rto_courses.college_id', $filterArray->college_id)
            ->whereHas('intakes.campusIntakes', function ($query) use ($filterArray) {
                if ($filterArray->campus) {
                    $query->where('campus_id', $filterArray->campus);
                }
                if ($filterArray->years) {
                    $query->whereIn('intake_year', $filterArray->years);
                }
            });
        if ($filterArray->campus) {
            $coursesQuery->where('rto_course_campus.campus_id', $filterArray->campus);
        }
        if ($filterArray->years) {
            $coursesQuery->whereIn('rto_course_intake_dates.intake_year', $filterArray->years);
        }
        if ($filterArray->courses) {
            $coursesQuery->whereIn('rto_courses.id', $filterArray->courses);
        }
        if ($filterArray->search) {
            $coursesQuery->where(function ($query) use ($filterArray) {
                $query->where('rto_courses.course_code', 'LIKE', $filterArray->search.'%')
                    ->orWhere('rto_courses.course_name', 'LIKE', '%'.$filterArray->search.'%')
                    ->orWhere('rto_course_intake_dates.intake_name', 'LIKE', '%'.$filterArray->search.'%');
            });
        }
        if ($courseId) {
            return $coursesQuery->find($courseId);
        }
        $data = $coursesQuery->groupBy('rto_courses.id')->get();

        return $data;
    }

    public function updateCourseIntakes($data)
    {
        $college_id = Auth::user()->college_id;
        $allowedActions = ['edit', 'delete'];
        $action = $data['action'] ?? 'edit';
        DB::beginTransaction();
        try {
            if (! in_array($action, $allowedActions)) {
                throw new \Exception('Unknown request made.');
            }
            $checkedCampuses = array_column(array_filter($data['intakes_for'], function ($item) {
                return $item['checked'];
            }), 'id');
            $allYears = array_column($data['years'], 'id');
            $checkedYears = array_column(array_filter($data['years'], function ($item) {
                return $item['checked'];
            }), 'id');
            $uncheckedYears = array_column(array_filter($data['years'], function ($item) {
                return ! $item['checked'];
            }), 'id');
            $courseId = $data['course']['id'] ?? 0;
            $coursesList = Courses::with([
                'intakes' => function ($query) use ($allYears, $checkedCampuses) {
                    $query->whereIn('intake_year', $allYears)->with([
                        'campusIntakes' => function ($query) use ($checkedCampuses) {
                            $query->whereIn('campus_id', $checkedCampuses);
                        },
                    ]);
                },
            ])
                ->where('college_id', $college_id)
                ->where('id', $courseId)
                ->first();
            if (! $coursesList) {
                throw new \Exception('Course does not exist.');
            }
            $intakes = $coursesList->intakes;
            $updated = $enabled = 0;
            foreach ($intakes as $intake) {
                // for this particular intake if the year is unchecked; disable the inatakes
                // else enable the intakes
                // after updating check if all campus intakes are disabled if disabled update the intake as disabled
                // this process is done to solve anu compatibility issues
                $year = Carbon::parse($intake->intake_start)->year;
                $status = 0;
                if (in_array($year, $allYears)) {
                    $status = in_array($year, $checkedYears) ? 1 : 0;
                } else {
                    continue;
                }
                $intakeStart = Carbon::today();
                if ($intake->intake_start) {
                    $intakeStart = Carbon::parse($intake->intake_start);
                }
                if ($intake->campusIntakes) {
                    foreach ($intake->campusIntakes as $campus) {
                        $campus->status = $status;
                        if ($action == 'delete') {
                            // delete only the checked years
                            // and intake of future only
                            $saved = ($status == 1 && $intakeStart->isFuture()) ? $campus->delete() : false;
                        } else {
                            $saved = $campus->save();
                        }
                        if ($saved) {
                            $updated++;
                            $enabled += $status;
                        }
                    }
                }
                $sumStatus = CampusIntake::where('intake_id', $intake->id)->sum('status');
                if ($action == 'delete' && $sumStatus == 0) {
                    // if there are not any campuses left (intake deleted from all campuses)
                    // then delete the intake (deletion happens only if the request was to delete the intake)
                    $intake->delete();
                } else {
                    $intake->active = ($sumStatus > 0) ? 1 : 0;
                    $intake->save();
                }
            }
            // dd($coursesList->intakes->toArray());
            DB::commit();

            return ($action == 'delete') ? ['course' => $courseId, 'deleted' => $updated] : ['course' => $courseId, 'updated' => $updated, 'enabled' => $enabled];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function checkIntakeAction(CoursesIntakeDate $intake, $campus = 0, $checkfor = 'edit')
    {
        // logic to check if the intake is allowed to edit/delete or not
        // for not just let everyting
        return true;
    }

    public function saveIntake($data)
    {
        if ($data['action'] == 'delete') {
            return $this->deleteIntake($data);
        }
        $request = new Request($data);
        $validatedData = $request->validate([
            'id' => [
                'nullable',
                'integer',
                Rule::exists('App\Model\v2\CoursesIntakeDate', 'id')->where(function ($query) use ($data) {
                    $query->where('course_id', $data['course_id']);
                }),
            ],
            'course_id' => [
                'required',
                'integer',
                Rule::exists('App\Model\v2\Courses', 'id')->where(function ($query) {
                    $query->where('college_id', Auth::user()->college_id);
                }),
            ],
            'intake_name' => 'required|string',
            'intake_receiver' => 'required',
            'intake_start' => [
                'required',
                'date',
                'after:today',
                function ($attribute, $value, $fail) use ($data) {
                    $value = Carbon::parse($value)->format('Y-m-d');
                    $exists = CoursesIntakeDate::where('course_id', $data['course_id'])
                        ->whereDate('intake_start', $value)
                        ->when($data['id'], function ($query) use ($data) {
                            $query->where('id', '!=', $data['id']);
                        })->exists();
                    if ($exists) {
                        $fail('An intake date already exists in the given date. A course can not have multiple intake dates in a same date.');
                    }
                },
            ],
            'intake_year' => 'nullable',
            'active' => 'nullable',
            'campuses' => 'nullable',
            'action' => 'nullable',
        ]);
        /* if the request is validated */
        DB::beginTransaction();
        try {
            // get course details
            $course = Courses::With(['campuses'])->find($validatedData['course_id']);
            $courseCampuses = $course->campuses->toArray() ?? [];
            $checkedCampuses = array_column(array_filter($validatedData['campuses'], function ($item) {
                return $item['checked'];
            }), 'id');
            $courseCampuses = array_column(array_filter($courseCampuses, function ($item) {
                return $item['id'];
            }), 'campus_id');
            $intakeStart = Carbon::parse($validatedData['intake_start']);
            $intakeEnd = $this->getEndDate($intakeStart, $course->course_duration, $course->couse_duration_type);
            /* now prepare a model to insert/update intake */
            $intake = CoursesIntakeDate::find($validatedData['id']);
            if (! $intake) {
                $intake = new CoursesIntakeDate;
                $intake->college_id = Auth::user()->college_id;
                $intake->campus_id = 0;
                $intake->course_type = $course->course_type_id;
                $intake->course_id = $course->id;
                $intake->created_by = Auth::user()->id;
            } else {
                $intake->updated_by = Auth::user()->id;
            }
            $intake->intake_year = $intakeStart->year;
            $intake->intake_name = $validatedData['intake_name'];
            $intake->intake_start = $intakeStart->format('Y-m-d');
            $intake->intake_end = $intakeEnd;
            $intake->intake_duration = $course->course_duration;
            $intake->intake_receiver = $validatedData['intake_receiver'];
            $intake->active = $validatedData['active'] ? 1 : 0;
            $intake->batch_number = null;
            $saved = $intake->save();
            // after saving the intake now save the intake campuses
            if ($saved && ! empty($checkedCampuses)) {
                foreach ($checkedCampuses as $campus) {
                    if (! in_array($campus, $courseCampuses)) {
                        continue;
                    }
                    $condition = [
                        'intake_id' => $intake->id,
                        'campus_id' => $campus,
                    ];
                    $values = [
                        'status' => $validatedData['active'] ? 1 : 0,
                    ];
                    CampusIntake::updateOrCreate($condition, $values);
                }
            }
            $sumStatus = CampusIntake::where('intake_id', $intake->id)->sum('status');
            $intake->active = ($sumStatus > 0) ? 1 : 0;
            $intake->save();
            DB::commit();

            return ['course' => $course->id, 'saved' => $saved];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteIntake($data)
    {
        $request = new Request($data);
        $validatedData = $request->validate([
            'id' => [
                'nullable',
                'integer',
                Rule::exists('App\Model\v2\CoursesIntakeDate', 'id')->where(function ($query) use ($data) {
                    $query->where('course_id', $data['course_id']);
                }),
            ],
            'course_id' => [
                'required',
                'integer',
                Rule::exists('App\Model\v2\Courses', 'id')->where(function ($query) {
                    $query->where('college_id', Auth::user()->college_id);
                }),
            ],
            'campuses' => 'nullable',
        ]);
        /* if the request is validated */
        DB::beginTransaction();
        try {
            $intakeDate = CoursesIntakeDate::with('campusIntakes')->find($validatedData['id']);
            $courseId = $intakeDate->course_id ?? 0;
            $campusIntakes = $intakeDate->campusIntakes ?? [];
            $checkedCampuses = array_column(array_filter($validatedData['campuses'], function ($item) {
                return $item['checked'];
            }), 'id');
            $totalCampuses = $campusDeleted = 0;
            foreach ($campusIntakes as $intake) {
                $campusId = $intake->campus_id;
                $allowed = $this->checkIntakeAction($intakeDate, $campusId, 'delete');
                if (in_array($campusId, $checkedCampuses) && $allowed) {
                    $deleted = $intake->delete();
                    if ($deleted) {
                        $campusDeleted++;
                    }
                }
                $totalCampuses++;
            }
            $deleted = false;
            if ($totalCampuses == $campusDeleted) {
                $deleted = $intakeDate->delete();
            }
            DB::commit();

            return ['course' => $courseId, 'deleted' => $deleted, 'campuses_deleted' => $campusDeleted];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    /* course units setup related functions below */
    public function getCourseTypes($college_id = false)
    {
        $collegeFilter = [0];
        if ($college_id > 0) {
            $collegeFilter[] = $college_id;
        }
        $courseTypes = CourseType::Where('status', 1);
        if ($college_id) {
            $courseTypes->WhereIn('college_id', $collegeFilter);
        }

        $courseTypes = $courseTypes->select('id', 'title')->get()->map(function ($item) {
            return [
                'value' => (int) $item->id,
                'label' => $item->title,
            ];
        });

        return $courseTypes;
    }

    public function adjustOldUnits($course, $units, $subjects)
    {
        $courseId = $course->id;
        $courseType = $course->course_type_id;
        // set master units data
        $unitData = $mappedData = $subjectData = $subjectMappedData = [];
        foreach ($units as $unit) {
            if ($unit->status !== 1) {
                continue;
            }
            $imported = Units::import($unit->toArray());
            $unitData[$unit->id] = [
                'subject_id' => $unit->subject_id,
                'course_id' => $unit->course_id,
                'new_master_id' => $imported->id,
                'imported' => $imported,
            ];
            $mappedData[$imported->id] = $unit->id;
        }
        // imports subjects
        foreach ($subjects as $subject) {
            $importData = $subject->toArray();
            $importData['course_id'] = $courseId;
            $imported = CourseSubjects::import($importData);
            $subjectData[$subject->id] = [
                'course_id' => $imported->course_id,
                'new_master_id' => $imported->id,
                'imported' => $imported,
            ];
            $subjectMappedData[$imported->id] = $subject->id;
        }
        /* now map the units to subject */
        foreach ($unitData as $ind => $val) {
            $subjectId = $val['subject_id'] ?? null;
            $newSubjectData = $subjectData[$subjectId] ?? [];
            $newSubjectId = $newSubjectData['new_master_id'] ?? null;
            $newUnitId = $val['new_master_id'] ?? null;
            if ($newUnitId) {
                // insert the new mapping data
                $unit = $val['imported'] ?? [];
                $subject = $newSubjectData['imported'] ?? [];
                $mapped = SubjectUnits::mapSubjects($unit, $subject, $courseId);
                if ($mapped) {
                    $subjectData[$subjectId]['mapped'] = true;
                    $unitData[$ind]['mapped'] = true;
                } else {
                    $subjectData[$subjectId]['mapped'] = false;
                    $unitData[$ind]['mapped'] = false;
                }
            }
        }

    }

    /*
    get all the units in the selecte course
    Latest units as per government api are fetched
    each unit information returned along with the setting values saved by the college for the units
    */
    /*
    new function to get unit list for incorporating templates
    */
    public function getCourseUnitDetails(CourseTemplate $templateData, $newCourseCode = null)
    {
        DB::beginTransaction();
        $college_id = Auth::user()->college_id;
        $courseId = $templateData->course_id ?? null;
        $filterArray = ['college_id' => $college_id, 'id' => $courseId];
        /*
        if the course is already saved and if the course is not moved to new course setup;
        migrate it to new course setup before every other procedures done
        */

        // $this->courseunits->migrateUnitsToNewSetup($templateData);
        // old logic replaced with new sync service
        SyncUnitsSetupService::SyncUnitsFromOldSetupToNew($templateData);

        if (! empty($templateData)) {
            // after the migration is done; again re-generate the template data
            $templateData = CourseTemplate::getTemplateData($courseId, $templateData->id);
        }

        $courseData = Courses::with(['subjects', 'subjects.subject_units'])->Where($filterArray)
            ->first();
        $courseCode = $courseData->course_code ?? null;
        if (empty($courseCode) && empty($newCourseCode)) {
            return [];
        } else {
            $courseCode = $newCourseCode;
        }
        $courseDetail = $this->courseunits->loadUnits($courseData, $newCourseCode, $templateData);
        $finalData = $this->prepareUnitDataForResponse($courseData, $courseDetail, $templateData);

        DB::commit();

        return $finalData->toArray();
    }

    public function prepareUnitDataForResponse(?Courses $courseData, ApiResponseCourseDTO $courseDetail, ?CourseTemplate $templateData = null): ?ApiResponseCourseDTO
    {
        $college_id = Auth::user()->college_id;
        $unitsFromApi = collect($courseDetail->units ?? []);

        $templateUnitRecords = $templateData->templateunits ?? collect([]);
        $templateSubjectRecords = $templateData->templatesubjects ?? collect([]);

        $templateSubjectIds = $templateSubjectRecords->pluck('subject_id')->toArray();

        $templateUnits = $unitsFromApi->filter(function ($unit) {
            return $unit->templateData !== null;
        });

        $courseSubjects = ($courseData) ? $courseData->subjects->keyBy('id') : [];

        $courseTypes = $this->getCourseTypes($college_id)->pluck('label', 'value')->toArray();

        $courseDetail->CourseTypeId = $courseData->course_type_id ?? 0;
        $courseDetail->CourseType = $courseTypes[$courseDetail->CourseTypeId] ?? '';

        $allCoreUnits = $unitsFromApi->filter(function ($unit) {
            return $unit->IsEssential == true;
        });
        $allElectiveUnits = $unitsFromApi->filter(function ($unit) {
            return $unit->IsEssential == false;
        });
        $unitsAdded = $templateUnits->filter(function ($unit) {
            return $unit->Added == true;
        });
        $unitsAdded->each(function ($unit) use ($templateUnitRecords) {
            $templateUnitData = $templateUnitRecords->where('unit_id', $unit->ID)->first();
            $unitType = $unit->Type == SiteConstants::CORETEXT ? SiteConstants::CORETEXT : SiteConstants::ELECTIVETEXT;
            $templateUnitType = $templateUnitData->unit_type;
            $unit->Type = ! empty($templateUnitType) ? $templateUnitType : $unitType;
            $unit->TypeDifferent = $templateUnitType !== $unitType;
            $unit->OriginalType = $unitType;
            $unit->IsEssential = $templateUnitType === SiteConstants::CORETEXT;
            $unit->unit_details['unit_type'] = ! empty($templateUnitType) ? $templateUnitType : $unitType;
            $unit->unit_details['unit_type_original'] = $unitType;
            $unit->unit_details['unit_type_different'] = $templateUnitType !== $unitType;
            $unit->subject_details = $unit->unit_details->subject ?? null;
        });

        $coreAdded = $unitsAdded->filter(function ($unit) {
            return $unit->IsEssential == true;
        });

        $electiveAdded = $unitsAdded->filter(function ($unit) {
            return $unit->IsEssential == false;
        });

        $coreUnitsCount = $coreAdded->count();
        $electiveUnitsCount = $electiveAdded->count();

        $implementedUnits = $unitsAdded->filter(function ($unit) {
            return $unit->primary_id > 0;
        });
        $orphanUnits = $unitsAdded->filter(function ($unit) {
            return empty($unit->primary_id);
        });
        $unitsGroup = ($templateUnits) ? $templateUnits->filter(function ($item) {
            return $item->Added == true;
        })->groupBy('primary_id') : [];
        if ($courseSubjects) {
            $courseSubjects = $courseSubjects->map(function ($item) use ($templateSubjectIds) {
                if (in_array($item->id, $templateSubjectIds)) {
                    $item['is_in_template'] = true;
                } else {
                    $item['is_in_template'] = false;
                }
                $item['subject_id'] = $item->synced_subject;
                $item['course_subject_id'] = $item->synced_for;
                // $item['assigned_units'] = $unitsGroup->get($item['id'], []);
                $item['assigned_units'] = $item->subject_units->map(function ($unit) {
                    return [
                        'ID' => $unit->id,
                        'Code' => $unit->unit_code,
                        'Title' => $unit->unit_name,
                        'unit_code' => $unit->unit_code,
                        'unit_name' => $unit->unit_name,
                        'Type' => $unit->unit_type,
                        'Source' => $unit->unit_source,
                        'synced_for' => $unit->synced_for,
                        'course_subject_id' => $unit->course_subject_id,
                    ];
                });
                unset($item->subject_units);

                return $item;
            });
            $courseSubjects = $courseSubjects->where('is_in_template', true);
        }
        $coursesList = [
            'course_id' => $courseData->id ?? null,
            'course_code' => $courseData->course_code ?? null,
            'total' => $unitsFromApi->count(),
            'core_count' => $allCoreUnits->count(),
            'core' => $allCoreUnits->values(),
            'elective' => $allElectiveUnits->values(),
            'elective_count' => $allElectiveUnits->count(),
            'template_total' => $templateUnits->count(),
            'core_added' => $coreAdded->count(),
            'elective_added' => $electiveAdded->count(),
            'units' => $implementedUnits->values(),
            'orphans' => $orphanUnits->values(),
        ];

        $courseDetail->units = $coursesList;

        return $courseDetail;
    }

    public function getCourseEditDetails($courseCode = '', $getBy = 'code', $forceSync = false)
    {
        if (empty($courseCode)) {
            return null;
        }
        $checkField = ($getBy == 'id') ? 'id' : 'course_code';
        // dd($existingCourse)
        $college_id = Auth::user()->college_id;
        $existingCourse = Courses::With(['faculty', 'department'])->Where('college_id', $college_id)
            ->Where($checkField, $courseCode)
            ->first();
        if ($forceSync == false && empty($existingCourse)) {
            return null;
        }
        $courseIntakes = [];
        if ($existingCourse->course_type_id == SiteConstants::SHORT_COURSE_TYPE_ID) {
            $courseIntakes = CoursesIntakeDate::with(['creator', 'updater', 'campusIntakes.campus'])->where('course_id', $existingCourse->id)->paginate(SiteConstants::DEFAULT_PER_PAGE);
        }
        // check if any avetmiss info missing..
        // check avetmiss data for all vet courses
        $checkAVETMISS = false;
        $vetCourseTypeId = config('constants.vet_course_type_id');
        $higherEdCourseTypeId = config('constants.highered_course_type_id');
        // if coursecode is availalbe and vet synced is off for vet_courses do not sync from server
        if (empty($existingCourse) && ! empty($courseCode)) {
            // check for api
            $checkAVETMISS = true;
        } elseif ($existingCourse) {
            $isVet = $existingCourse->course_type_id == $vetCourseTypeId;
            $isSyncable = $existingCourse->is_vet_synced === null || $existingCourse->is_vet_synced === 1;
            $checkAVETMISS = $isVet && $isSyncable;
            $courseCode = $existingCourse->national_code ?? ($existingCourse->national_code ?? '');
        }
        if ($checkAVETMISS || $forceSync == true) {
            // call for training.gov.au for vet course detail
            $coursesApi = new CoursesApiController;
            $resArray = [];
            $courseDetail = $coursesApi->nationCodeApiCall($courseCode);
            // api result data
            $apiData = $courseDetail['GetDetailsResult'] ?? [];
            if ($apiData) {
                // reset the values if data received from the api
                if (empty($existingCourse)) {
                    $existingCourse = new Courses;
                }
                $existingCourse->course_type_id = $vetCourseTypeId;
                $existingCourse->is_vet_synced = ($forceSync == true) ? 0 : 1;
                $existingCourse->course_code = $apiData['Code'] ?? '';
                $existingCourse->national_code = $apiData['Code'] ?? '';
                $existingCourse->course_name = $apiData['Title'] ?? '';
                $classifications = $apiData['Classifications']['Classification'] ?? [];
                for ($i = 0; $i < count($classifications); $i++) {
                    $clsfData = $classifications[$i] ?? [];
                    $schemeCode = $clsfData['SchemeCode'] ?? null;
                    $valueCode = $clsfData['ValueCode'] ?? null;
                    if ($schemeCode == 01) {
                        $existingCourse->ANZSCO_code = $valueCode;
                    } elseif ($schemeCode == 04) {
                        $existingCourse->field_of_education_id = $valueCode;
                    } elseif ($schemeCode == 05) {
                        $existingCourse->level_of_education_id = $valueCode;
                    } elseif ($schemeCode == 06) {
                        $existingCourse->course_recognition_id = $valueCode;
                    }
                }
                // $outputArray["anzscocode"] = $result["GetDetailsResult"]["Classifications"]["Classification"][0]["ValueCode"];
                $existingCourse->total_nominal_hours = $apiData['CreatedDate']['OffsetMinutes'] ?? null;
                $existingCourse->is_superseded = ! (($apiData['CurrencyStatus'] ?? '') == 'Current');
                $existingCourse->superseded_date = $apiData['CurrencyPeriods']['NrtCurrencyPeriod']['EndDate'] ?? null;
                $supersededBy = $apiData['ReverseMappingInformation']['Mapping'] ?? null;
                $existingCourse->superseded_course = $supersededBy;
                $existingCourse->is_super_code = $supersededBy['Code'] ?? '';
            }
        }
        // CurrencyStatus == "Current" current else superseeded
        // MappingInformation['mapping'] ==>> the courses inside superseeds this course
        // ReverseMappingInformation['mapping'] ==> the courses here are superseeded by this course if the course is current there will be no reverse mapping
        $generalInfo = new CourseGeneralResource($existingCourse);
        $feeHours = new CourseFeeResource($existingCourse);
        $faculty = new CourseFacultyResource($existingCourse);
        $avetmiss = new CourseAvetmissResource($existingCourse);
        $intakes = CourseShortCourseIntakesResource::collection($courseIntakes);
        $higherEdCourseArr = null;
        // dd($existingCourse);
        if (! empty($existingCourse) && $existingCourse->course_type_id == $higherEdCourseTypeId) {
            $higherEdCourseArr = HigheredCourseDetail::Where('course_id', $existingCourse->id)->first();
        }
        $higherEdCourseArr = new HigheredCourseResource($higherEdCourseArr);
        // if avetmiss info is missing the get the data from the api;
        $courseData = [
            'general' => $generalInfo,
            'feehours' => $feeHours,
            'faculty' => $faculty,
            'avetmiss' => $avetmiss,
            'intakes' => $intakes,
            'highered' => $higherEdCourseArr,
            'progress' => ! empty($existingCourse) ? $this->getProgressStatus($existingCourse) : null,
        ];

        return $courseData;
    }

    public function prepareResponseMessage(Courses $course, $tab = '')
    {
        $college_id = Auth::user()->college_id;
        $message = config_locale('messages.courses.updated');
        $warning = false;
        if (isset($course->activated)) {
            if ($course->activated == 'stop_activate') {
                $message = config_locale('messages.courses.updated_stop_activate');
                $warning = true;
            } elseif ($course->activated == 'stop_deactivate') {
                $message = config_locale('messages.courses.updated_stop_deactivate');
                $warning = true;
            } elseif ($course->activated == 'status_not_changed') {
                $message = config_locale('messages.courses.updated_status_not_changed');
                $warning = true;
            } elseif ($course->activated == 'activated') {
                $message = config_locale('messages.courses.updated_activated');
            } elseif ($course->activated == 'deactivated') {
                $message = config_locale('messages.courses.updated_deactivated');
            }
        }
        if ($tab == 'highered') {
            // return only message for higher ed tab request as
            return $message;
        }
        $data = $this->getCourseEditDetails($course->id, 'id');
        if ($tab == 'faculty') {
            $arrDepartments = Courses::getDepartments($college_id);
            $arrFaculties = Courses::getFaculties($college_id, false);
            $returnData = ['course' => $data, 'faculties' => $arrFaculties, 'departments' => $arrDepartments];
        } else {
            $returnData = ['course' => $data];
        }
        if ($warning) {
            return ajaxSuccessWithWarning($returnData, $message);
        }

        return ajaxSuccess($returnData, $message);
    }

    public function assignCourseToCampuses($data)
    {
        $courseId = $data['id'] ?? null;
        $collegeId = $data['college_id'] ?? null;
        if (empty($courseId)) {
            return false;
        }
        $campuses = $data['campus_list'] ?? '';
        $campuses = explode(',', $campuses);
        $allCampuses = Courses::getCampusList($collegeId, true);
        $saved = [];
        foreach ($allCampuses as $cmp) {
            if (in_array($cmp['id'], $campuses)) {
                $filtrData = [
                    'course_id' => $courseId,
                    'campus_id' => $cmp['id'],
                ];
                $d = CollegeCourse::updateOrCreate(
                    $filtrData,
                    ['college_id' => $collegeId]
                );
                $saved[] = $d->toArray();
            }
        }
        // remove the campuses if any
        $re = CollegeCourse::where(['college_id' => $collegeId, 'course_id' => $courseId])
            ->whereNotIn('campus_id', $campuses)
            ->delete();

    }

    public function saveUnitsNormalized($unit = [], $course = null)
    {
        $courseId = $course->id ?? null;
        if (empty($unit) || empty($courseId)) {
            return false;
        }
        $college_id = Auth::user()->college_id;
        $user_id = Auth::user()->id;
        // check for the unit if it is already availalbe in master unit table
        $masterUnitData = Units::Where('college_id', $college_id)
            ->Where('unit_code', $unit['Code'])
            ->first();
        if (! $masterUnitData) {
            $unit['unit_type_basic'] = $course->course_type_id ?? null;
            $unit['unit_source'] = 'api';
            $unit['unit_scope'] = 'public';
            // this unit is a new unit so add to our unit repository
            $masterUnitData = Units::saveNewUnitFromApi($unit);
        }
        $masterUnitId = $masterUnitData->id ?? 0;
        $status = (isset($unit['Added']) && $unit['Added'] == true) ? 1 : 0;
        // added unit data
        $unitData = SubjectUnits::Where('unit_id', $masterUnitId)
            ->Where('course_id', $courseId)
            ->first();
        $currentUnitStatus = $unitData->status ?? 0;
        /*
        check here if the unit can be unselected
        if status is changed from activated to deactivated
        if subject changed/removed
        need to check if the unit has timetable or not if it has timetable/batch then the unit should remain intact
        */
        if ($unitData && ! $unitData->canBeUpdated(['status' => $status])) {
            $unitData->process_result = 'updatefailed';

            return $unitData;
        }
        DB::beginTransaction();
        try {
            if (! $unitData) {
                $unitData = new SubjectUnits;
            }
            $masterUnitData->status = $status;
            $masterUnitData->unit_id = $unitData->unit_id ?? null;
            $masterUnitData->course_id = $unitData->course_id ?? null;
            $masterUnitData->course_subject_id = $masterUnitData->subject_id = $unitData->course_subject_id ?? null;
            $hasSpace = $unitData->checkUnitTypeQuota($masterUnitData);
            // echo $hasSpace ? "Yes there is space <br />" : " There is no space <br />";
            // just to know whether the unit is added or removed
            $action = 'notchanged';
            if ($currentUnitStatus == 0 && $unitData->status == 1) {
                $action = 'added';
            } elseif ($currentUnitStatus == 1 && $unitData->status == 0) {
                $action = 'removed';
            }
            if ($hasSpace) {
                $unitData->status = $status;
                $unitData->save();
                $unitData->process_result = $action;
            } else {
                $unitData->status = 0;
                $unitData->save();
                $unitData->process_result = 'failed';
            }
            DB::commit();

            return $unitData;
        } catch (\Exception $e) {
            DB::rollBack();

            return false;
        }

        return $unitData;
    }

    public function prepareTemplateUnitsList(CourseTemplate $template)
    {
        $courseId = $template->course_id ?? null;
        $course = Courses::find($courseId);
        $courseUnits = $this->getCourseUnitDetails($template);

        return ['template' => new CourseTemplatesResource($template), 'units' => $courseUnits];
    }

    public function saveCompetencyElements(CompetencyCriteriaDTO $data)
    {
        $id = $data->id ?? null;
        $item = null;
        if ($id) {
            $item = ElementOfUnitCompetency::find($id);
            if ($item) {
                $item->update($data->toArray());
            }
        } else {
            $item = new ElementOfUnitCompetency($data->toArray());
        }
        if ($item === null) {
            return 'cnotsaved';
        }
        $saved = $item->save();

        return $saved;
    }

    public function deleteCompetencyElements($data = null)
    {
        $item = ElementOfUnitCompetency::where($data)->first();
        $deleted = false;
        if ($item) {
            $deleted = $item->delete();
        }

        return $deleted;
    }

    public function assignSoloSubjectToUnit(SubjectUnits $unit, Courses $course, $subjectData = [], ?CourseTemplate $templateData = null)
    {
        $deliveryMode = $subjectData['delivery_mode'] ?? $unit->delivery_mode ?? null;
        $deliveryModeSaperated = str_split($deliveryMode);

        $internal = $deliveryModeSaperated[0] ?? $unit->internal ?? 'N';
        $external = $deliveryModeSaperated[1] ?? $unit->external ?? 'N';
        $workplaceBasedDelivery = $subjectData[2] ?? $unit->workplace_based_delivery ?? 'N';

        if (empty($deliveryMode)) {
            $deliveryMode = $internal.$external.$workplaceBasedDelivery;
        }

        $insertData = [
            'course_id' => $course->id ?? null,
            'subject_code' => $unit->unit_code ?? '',
            'subject_name' => $unit->unit_name ?? '',
            'subject_type' => $subjectData['subject_type'] ?? unit->unit_type ?? 'Elective',
            'grading_type' => $subjectData['grading_type'] ?? null,
            'contact_hours' => $subjectData['contact_hours'] ?? null,
            'level' => $subjectData['level'] ?? null,
            'is_long_indicator' => $subjectData['is_long_indicator'] ?? null,
            'is_assessment' => $subjectData['is_assessment'] ?? null,
            'subject_fee' => $subjectData['subject_fee'] ?? null,
            'domestic_subject_fee' => $subjectData['domestic_subject_fee'] ?? null,
            'delivery_mode' => $deliveryMode,
            'internal' => $internal,
            'external' => $external,
            'workplace_based_delivery' => $workplaceBasedDelivery,
            'EFTSL_study_load' => $subjectData['EFTSL_study_load'] ?? null,
            'is_active' => $subjectData['is_active'] ?? null,
            'inc_in_certificate' => $subjectData['inc_in_certificate'] ?? null,
            'subject_display_order' => 999,
        ];
        $subject = CourseSubjects::syncSubject($insertData);
        if ($subject) {
            // DB::enableQueryLog();
            $unit->update(['course_subject_id' => $subject->id ?? null]);
            // dd(DB::getQueryLog());
            if ($templateData) {
                $templateData->mapSubjectToTemplate($subject, true);
            }

            return null; // $unit;
        }

        return false;
    }

    public function loadUnitsToCopy(CourseTemplate $template)
    {
        $templateUnits = $template->templateunits;
        $templateSubjects = $template->templatesubjects;

        $excludeUnits = $excludeSubjects = [];
        if ($templateUnits) {
            $excludeUnits = $templateUnits->pluck('unit_id');
        }
        if ($templateSubjects) {
            $excludeSubjects = $templateSubjects->pluck('subject_id');
        }

        $templates = CourseTemplate::with(['templateunits' => function ($squery) use ($excludeUnits) {
            $squery->whereNotIn('rto_course_template_structure.unit_id', $excludeUnits)->with('unitdata');
        }, 'templatesubjects' => function ($squery) use ($excludeSubjects) {
            $squery->whereNotIn('rto_course_template_subjects.subject_id', $excludeSubjects)->with(['subjectdata' => function ($squery) {
                $squery->withCount('subject_units');
            }]);
        }])->where([
            'course_id' => $template->course_id,
            'set_active' => 1,
        ])
            ->where('id', '<>', $template->id)
            ->get();

        return TemplateUnitsCopyResource::collection($templates);
    }

    public function CopyUnitsToTemplate(CourseTemplate $template, $units)
    {
        $coreNumbers = $template->no_of_core_subject;
        $electiveNumbers = $template->no_of_elective_subject;
        $masterTemplateUnits = $template->templateunits()->get();
        $addedUnits = $masterTemplateUnits->pluck('unit_id')->toArray();
        $addedCore = $masterTemplateUnits->where('unit_type', SiteConstants::CORETEXT)->count();
        $addedElective = $masterTemplateUnits->where('unit_type', SiteConstants::ELECTIVETEXT)->count();
        $copiedUnits = 0;
        foreach ($units as $unit) {
            $canBeAdded = false;
            if ($unit['unit_type'] == SiteConstants::CORETEXT && $addedCore < $coreNumbers) {
                $canBeAdded = true;
            } elseif ($unit['unit_type'] == SiteConstants::ELECTIVETEXT && $addedElective < $electiveNumbers) {
                $canBeAdded = true;
            }
            if ($canBeAdded) {
                $saveData = [
                    'college_id' => $template->college_id ?? 0,
                    'course_template_id' => $template->id ?? 0,
                    'course_id' => $template->course_id ?? 0,
                    'unit_id' => $unit['unit_id'] ?? 0,
                    'unit_type' => $unit['unit_type'] ?? 0,
                    'is_active' => $unit['is_active'] ?? 0,
                ];
                $structure = new CourseTemplateStructure($saveData);
                $saved = $structure->save();
                if ($saved) {
                    $copiedUnits++;
                    if ($unit['unit_type'] == SiteConstants::CORETEXT) {
                        $addedCore++;
                    }
                    if ($unit['unit_type'] == SiteConstants::ELECTIVETEXT) {
                        $addedElective++;
                    }
                }
            }
        }

        return $copiedUnits;
    }

    public function copySubjectsToTemplate(CourseTemplate $template, $subjects)
    {
        $coreNumbers = $template->no_of_core_subject;
        $electiveNumbers = $template->no_of_elective_subject;
        $course = Courses::find($template->course_id);
        if ($course->course_type_id == SiteConstants::HIGHERED_COURSE_TYPE_ID) {
            $coreAdded = $template->templatecoresubjects_count ?? 0;
            $electiveAdded = $template->templateelectivesubjects_count ?? 0;
        } else {
            $coreAdded = $template->templatecoreunits_count ?? 0;
            $electiveAdded = $template->templateelectiveunits_count ?? 0;
        }
        $totalAdded = $coreAdded + $electiveAdded;
        $copiedSubjects = 0;
        foreach ($subjects as $subject) {
            $canBeAdded = false;
            $subjectType = $subject['subject_type'] == SiteConstants::CORETEXT ? SiteConstants::CORETEXT : SiteConstants::ELECTIVETEXT;
            $subject = CourseSubjects::with('subject_units')->withCount('subject_units')->find($subject['subject_id']);
            $subject->subject_type = $subjectType;

            if ($course->course_type_id == SiteConstants::HIGHERED_COURSE_TYPE_ID) {
                /* if course is higher ed, then one subject is counted as one single unit */
                $spaceRequired = 1;
            } else {
                /* if course is not higher ed, then the space required is the number of units in the subject */
                $spaceRequired = $subject->subject_units_count;
            }
            if ($subject['subject_type'] == SiteConstants::CORETEXT && ($coreNumbers - $coreAdded) >= $spaceRequired) {
                $canBeAdded = true;
            } elseif ($subject['subject_type'] == SiteConstants::ELECTIVETEXT && ($electiveNumbers - $electiveAdded) >= $spaceRequired) {
                $canBeAdded = true;
            }

            if ($canBeAdded) {
                $addedSubject = $template->mapSubjectToTemplate($subject, true);
                if ($addedSubject) {
                    $copiedSubjects++;
                    $template->loadCount(['templatecoreunits', 'templateelectiveunits', 'templatecoresubjects', 'templateelectivesubjects']);
                    if ($course->course_type_id == SiteConstants::HIGHERED_COURSE_TYPE_ID) {
                        $coreAdded = $template->templatecoresubjects_count ?? 0;
                        $electiveAdded = $template->templateelectivesubjects_count ?? 0;
                    } else {
                        $coreAdded = $template->templatecoreunits_count ?? 0;
                        $electiveAdded = $template->templateelectiveunits_count ?? 0;
                    }
                    $totalAdded = $coreAdded + $electiveAdded;
                }
            }
        }

        return $copiedSubjects;
    }

    public function saveShortCourseIntake(IntakeDataDTO $data)
    {
        $intakeId = $data->id ?? null;
        $course = Courses::with('campuses')->find($data->course_id);
        $campuses = [];
        $campuses = $course->campuses->whereIn('campus_id', $data->campuses)->pluck('campus_id')->toArray();

        if ($intakeId) {
            $intake = CoursesIntakeDate::findOrFail($intakeId);
            $saveData = $data->toArray();
            $saveData['campus_id'] = 0;
            $intake->fill($saveData);
            $intake->save();
            $intake->applyToCampuses($campuses);

            return $intake;
        } else {
            // if the intake is recurring then we need to loop through the data and save each intake
            $startDate = Carbon::parse($data->intake_start);
            $endDate = Carbon::parse($data->intake_end);

            $classStart = Carbon::parse($data->class_start_date);
            $classEnd = Carbon::parse($data->class_end_date);

            $classStartTime = Carbon::parse($data->class_start_time);
            $classEndTime = Carbon::parse($data->class_end_time);

            if ($data->recurring_class == 'daily' || $data->recurring_class == 'weekly' || $data->recurring_class == 'monthly') {
                $recurringEnd = Carbon::parse($data->recurring_end_date);
            } else {
                $recurringEnd = $startDate->copy()->subDay();
            }
            $saveData = $data->toArray();
            $saveData['campus_id'] = 0;
            do {
                $intake = new CoursesIntakeDate($saveData);
                $intake->save();
                $intake->applyToCampuses($campuses);
                if ($data->recurring_class == 'daily') {
                    $startDate->addDay();
                    $endDate->addDay();
                    $classStart->addDay();
                    $classEnd->addDay();
                } elseif ($data->recurring_class == 'weekly') {
                    $startDate->addWeek();
                    $endDate->addWeek();
                    $classStart->addWeek();
                    $classEnd->addWeek();
                } elseif ($data->recurring_class == 'monthly') {
                    $startDate->addMonth();
                    $endDate->addMonth();
                    $classStart->addMonth();
                    $classEnd->addMonth();
                }
                $saveData['intake_start'] = $startDate->format('Y-m-d');
                $saveData['intake_end'] = $endDate->format('Y-m-d');
                $saveData['class_start_date'] = $classStart->format('Y-m-d');
                $saveData['class_end_date'] = $classEnd->format('Y-m-d');
                $saveData['class_start_time'] = $classStartTime->format('H:i');
                $saveData['class_end_time'] = $classEndTime->format('H:i');

            } while ($startDate <= $recurringEnd);
        }

    }

    public function getShortCourseIntakes($courseId, $type = 'id', $filters = [])
    {
        if ($type == 'id') {
            $course = Courses::find($courseId)->id ?? null;
        } else {
            $course = Courses::where('course_code', $courseId)->value('id') ?? null;
        }
        if (! $course) {
            return 'nocourse';
        }
        $query = CoursesIntakeDate::with(['creator', 'updater', 'campusIntakes.campus'])->where('course_id', $course);
        /* apply filters to the query */
        /* filter by expired or upcomming or current */
        if ($filters['type'] == 'expired') {
            $query->where('intake_end', '<', Carbon::now());
        } elseif ($filters['type'] == 'upcoming') {
            $query->where('intake_start', '>', Carbon::now());
        } elseif ($filters['type'] == 'current') {
            $query->where('intake_start', '<=', Carbon::now())->where('intake_end', '>=', Carbon::now());
        }
        /* filter by active or inactive */
        if ($filters['status'] == 'active') {
            $query->where('active', 1);
        } elseif ($filters['status'] == 'inactive') {
            $query->where('active', 0);
        }
        /* filter by search text */
        if (! empty($filters['search'])) {
            /* check if the search text is a month and year */
            $months = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December',
            ];
            $search = [];

            $monthPattern = implode('|', $months);

            // Match only if the entire input is exactly "Month Year" or "Year Month"
            $pattern = '/^\s*('.$monthPattern.')\s+(\d{4})\s*$/i';
            $patternReverse = '/^\s*(\d{4})\s+('.$monthPattern.')\s*$/i';
            $date = $search = null;
            if (preg_match($pattern, $filters['search'], $matches)) {
                $date = $matches[2].' '.$matches[1].'-01';
            } elseif (preg_match($patternReverse, $filters['search'], $matches)) {
                $date = $matches[1].' '.$matches[2].'-01';
            } else {
                $search = trim($filters['search']);
            }

            if ($date) {
                $date = Carbon::parse($date);
                $query->where('intake_year', '=', $date->year)
                    ->whereMonth('intake_start', $date->month);
            }

            if ($search) {
                $query->where(function ($query) use ($search) {
                    $query->where('intake_name', 'like', '%'.$search.'%')
                        ->orWhere('intake_receiver', 'like', $search.'%')
                        ->orWhere('intake_start', 'like', $search.'%')
                        ->orWhere('intake_end', 'like', $search.'%');
                });
            }
        }

        return $query->paginate($filters['take'] ?? SiteConstants::DEFAULT_PER_PAGE, ['*'], 'page', $filters['page'] ?? 1);
    }

    public function searchSubjects($data, $type, $courseId = null)
    {
        $addedSubjectCodes = CourseSubjects::where('course_id', $courseId)->pluck('subject_code')->toArray();
        $query = CourseSubjects::join('rto_courses', 'rto_courses.id', '=', 'rto_course_subjects.course_id')
            ->withCount('subject_units')
            ->where(function ($query) use ($data) {
                $query->where('subject_name', 'like', '%'.$data.'%')
                    ->orWhere('subject_code', 'like', '%'.$data.'%');
            })
            ->where('is_active', 1)
            ->where('rto_courses.course_type_id', $type);
        if ($courseId) {
            $query->where('rto_courses.id', '!=', $courseId);
        }
        if (! empty($addedSubjectCodes)) {
            $query->whereNotIn('subject_code', $addedSubjectCodes);
        }

        return $query->limit(10)->get();
    }

    public function addSubjectsToCourse(Courses $course, $subjects = [], ?CourseTemplate $templateData = null)
    {
        $addedSubjects = [];
        // get all course subject codes
        $courseSubjectCodes = CourseSubjects::where('course_id', $course->id)->pluck('subject_code')->toArray();
        foreach ($subjects as $subject) {

            $subjectQry = CourseSubjects::with('subject_units')->where('course_id', '!=', $course->id);

            /* to stop any entry duplicate subjects in a course */
            if (! empty($courseSubjectCodes)) {
                $subjectQry->whereNotIn('subject_code', $courseSubjectCodes);
            }

            $subject = $subjectQry->find($subject['id']);

            if (empty($subject)) {
                continue;
            }
            $subjectUnits = $subject->subject_units->toArray() ?? [];
            $subject = $subject->toArray();

            $subject = array_diff_key($subject, array_flip([
                'id',
                'subject_units',
                'created_at',
                'updated_at',
                'created_by',
                'updated_by',
                'deleted_at',
            ]));
            $subject['course_id'] = $course->id;
            $newSubject = new CourseSubjects($subject);
            $newSubject->save();

            $newSubjectUnits = array_map(function ($unit) use ($newSubject) {
                $unit = array_diff_key($unit, array_flip([
                    'id',
                    'created_at',
                    'updated_at',
                    'created_by',
                    'updated_by',
                    'deleted_at',
                ]));
                $unit['course_id'] = $newSubject->course_id;
                $unit['course_subject_id'] = $newSubject->id;

                return $unit;
            }, $subjectUnits);

            foreach ($newSubjectUnits as $unit) {
                SubjectUnits::create($unit);
            }
            if ($templateData) {
                $templateData->mapSubjectToTemplate($newSubject, true);
            }
            $addedSubjects[] = $newSubject;
        }

        return $addedSubjects;
    }
}
