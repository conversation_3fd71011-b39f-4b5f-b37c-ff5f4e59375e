<?php

namespace App\Http\Controllers\Frontend;

use App;
use App\Http\Controllers\Controller;
use App\Model\CampusVenue;
use App\Model\CollegeDetails;
use App\Model\ContractCode;
use App\Model\CourseCalendar;
use App\Model\Courses;
use App\Model\CourseSite;
use App\Model\CourseTemplate;
use App\Model\CourseTraining;
use App\Model\Employer;
use App\Model\Semester;
use App\Model\SemesterDivision;
use App\Model\Staff;
use App\Model\StudentCourse;
use App\Model\Students;
use App\Model\StudentTraining;
use App\Model\TraineeUnitAssessment;
use App\Model\TrainieeshipActivityLog;
use App\Model\UnitModule;
use Auth;
use Config;
use DB;
use Helpers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Response;

class CourseTrainingController extends Controller
{
    public function __construct()
    {
        // $this->rememberToken
    }

    public function addCourseTrainingPlanInfo($studentId, $trainingPlanId, Request $request)
    {

        $courseTrainingId = (! empty($trainingPlanId) && $trainingPlanId > 0) ? $trainingPlanId : 0;

        $collegeId = Auth::user()->college_id;
        $arrStateList = Config::get('constants.arrState');
        $arrContractType = Config::get('constants.arrContractType');
        $arrTrainingStatus = Config::get('constants.arrTrainingStatus');
        $arrFundingSource = Config::get('constants.arrFundingSource');

        unset($arrStateList['00'], $arrStateList['09']);

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objCourses = new Courses;
        $arrCourseList = $objCourses->getCourseArrWithStatusForStudent($collegeId, $studentId);
        reset($arrCourseList);
        $data['firstCourseKey'] = key($arrCourseList);

        $objEmployer = new Employer;
        $arrEmployerList = $objEmployer->getEmployerNameList($collegeId);

        $objContractCode = new ContractCode;
        $arrContractCodeList = $objContractCode->getContractCodeArr($collegeId);

        $objCourseSite = new CourseSite;
        $arrCourseSiteList = $objCourseSite->getCourseSiteList($collegeId);

        $objCampusVenue = new CampusVenue;
        $arrCampusVenueList = $objCampusVenue->getVenueArrList($collegeId);

        if ($courseTrainingId > 0) {
            $objCourseTraining = new CourseTraining;
            $trainingInfo = $objCourseTraining->getCourseTrainingPlanInfo($collegeId, $courseTrainingId);
            $data['trainingInfo'] = $trainingInfo[0];
        }

        if ($request->isMethod('post') && $request->input('training_plan') == 'true') {

            $validator = Validator::make($request->all(), [
                'contract_date' => 'required',
                'contract_id' => 'required',
                'contract_type' => 'required',
                'contract_schedule_id' => 'required',
                'apprenticeship_id' => 'required',
                'apprenticeship_name' => 'required',
                'supervisor' => 'required',
                'booking_id' => 'required',
                'status' => 'required',
                'state' => 'required',
                // 'course_site'            => 'required',
                'funding_source' => 'required',
                // 'venue_code'            => 'required',
                'aac' => 'required',
                'note' => 'required',
                'training_contract_id' => 'required']);

            if ($validator->fails()) {
                return redirect(route('student-training-plan', ['student_id' => $studentId, 'id' => $courseTrainingId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objCourseTraining = new CourseTraining;

            if ($courseTrainingId > 0) {
                $objCourseTraining->updateTrainingPlanInfo($courseTrainingId, $request);
                $request->session()->flash('session_success', 'Course Training Plan Update Successfully.');
            } else {
                $objCourseTraining->saveTrainingPlanInfo($collegeId, $studentId, $request);
                $request->session()->flash('session_success', 'Course Training Plan Add Successfully.');
            }

            return redirect(route('student-training-plan', ['student_id' => $studentId, 'id' => '0']));
        }

        if ($request->isMethod('post') && $request->input('enroll_module') == 'true') {

            $unitIdArr = json_decode($request->input('selected_unit_id'));
            if (empty($unitIdArr)) {

                $validations['unit_id'] = 'required';
                $checkValidations = [
                    'unit_id.required' => 'Please select at least One Unit.',
                ];
                $validator = Validator::make($request->all(), $validations, $checkValidations);
                if ($validator->fails()) {
                    return redirect(route('student-training-plan', ['student_id' => $studentId, 'id' => $courseTrainingId]))
                        ->withErrors($validator)
                        ->withInput();
                }
            }

            foreach ($unitIdArr as $unitId) {
                $objStudentTraining = new StudentTraining;
                $objStudentTraining->saveStudentTrainingPlanInfo($collegeId, $studentId, $unitId, $request);
            }

            $request->session()->flash('session_success', 'Student Training Plan Add Successfully.');

            return redirect(route('student-training-plan', ['student_id' => $studentId, 'id' => '0']));
        }

        $data['header'] = [
            'title' => 'Student Training Plan',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Search Student' => route('search-students'),
                'Add Student Training Plan' => '',
            ]];
        $data['pagetitle'] = 'Add Student Training Plan';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['courseTraining.js'];
        $data['funinit'] = ['courseTraining.initInfo()'];

        $data['studentDetail'] = $arrStudentDetail;
        $data['arrStateList'] = $arrStateList;
        $data['arrCourseList'] = $arrCourseList;
        $data['arrContractType'] = $arrContractType;
        $data['arrTrainingStatus'] = $arrTrainingStatus;
        $data['arrEmployerList'] = $arrEmployerList;
        $data['arrCourseSiteList'] = $arrCourseSiteList;
        $data['arrContractCodeList'] = $arrContractCodeList;
        $data['arrFundingSource'] = $arrFundingSource;
        $data['arrCampusVenueList'] = $arrCampusVenueList;
        $data['studentId'] = $studentId;
        $data['courseTrainingId'] = $courseTrainingId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student_training.training-plan', $data);
    }

    public function deleteCourseTrainingPlanInfo($courseId, $studentId, $courseTrainingId, Request $request)
    {

        $collegeId = Auth::user()->college_id;

        $objStudentTraining = new StudentTraining;
        $arrExistedModuleList = $objStudentTraining->getStudentTrainingPlanArr($collegeId, $studentId, $courseId);

        // echo $arrExistedModuleList->count();exit;
        if ($arrExistedModuleList->count() > 0) {
            $request->session()->flash('session_error', 'First remove existed module training plan record.');
        } else {
            $objCourseTraining = new CourseTraining;
            $result = $objCourseTraining->deleteTrainingPlanInfo($collegeId, $studentId, $courseTrainingId);
            if ($result) {
                $request->session()->flash('session_success', 'Course Training Plan Delete Successfully.');
            } else {
                $request->session()->flash('session_error', 'Something will be Wrong. Please Try again.');
            }
        }

        return redirect(route('student-training-plan', ['student_id' => $studentId, 'id' => '0']));
    }

    public function deleteStudentTrainingPlanInfo($studentId, $studentTrainingId, Request $request)
    {

        $collegeId = Auth::user()->college_id;

        $objStudentTraining = new StudentTraining;
        $result = $objStudentTraining->deleteTrainingPlanInfo($collegeId, $studentId, $studentTrainingId);

        if ($result) {
            $request->session()->flash('session_success', 'Student Training Plan Delete Successfully.');
        } else {
            $request->session()->flash('session_error', 'Something will be Wrong. Please Try again.');
        }

        return redirect(route('student-training-plan', ['student_id' => $studentId, 'id' => '0']));
    }

    public function ajaxAction(Request $request)
    {

        $action = $request->input('action');
        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        switch ($action) {
            case 'getCourseTrainingPlanList':
                $studentId = $request->input('data.studentId');
                $courseId = $request->input('data.courseId');
                $this->getCourseTrainingPlanList($collegeId, $studentId, $courseId);
                break;
            case 'getTermBySemester':
                $semesterId = $request->input('data.semesterId');
                $termArr = $this->getTermList($collegeId, $semesterId);
                echo json_encode($termArr);
                break;
            case 'editExistedTrainingPlanInfo':
                $dataArr = $request->input('data');
                $objStudentTraining = new StudentTraining;
                $resultArr = $objStudentTraining->updateStudentTrainingPlanInfo($collegeId, $dataArr);
                echo json_encode($resultArr);
                break;
            case 'updateExistedTaskInfo':
                $dataArr = $request->input('data');
                $objUnitAssessment = new TraineeUnitAssessment;
                $resultArr = $objUnitAssessment->updateUnitAssessmentInfo($collegeId, $dataArr, $userId);
                echo json_encode($resultArr);
                break;
            case 'updateLockUnlockFinaliseInfo':
                $dataArr = $request->input('data');
                $objUnitAssessment = new TraineeUnitAssessment;
                $resultArr = $objUnitAssessment->updateLockUnlockFinaliseInfo($collegeId, $dataArr, $userId);
                echo json_encode($resultArr);
                break;
            case 'assignTask2Module':
                $dataArr = $request->input('data');
                $objUnitAssessment = new TraineeUnitAssessment;
                $resultArr = $objUnitAssessment->assignTask2Module($collegeId, $dataArr, $userId);
                echo json_encode($resultArr);
                break;
            case 'getArrExistedtaskList':
                $studentId = $request->input('data.studentID');
                $subjectUnitId = $request->input('data.subjectUnitID');
                $arrAssessmentCompetency = Config::get('constants.arrAssessmentCompetency');

                $objUnitAssessment = new TraineeUnitAssessment;
                $arrUnitAssessmentList = $objUnitAssessment->getUnitAssessmentList($collegeId, $studentId, $subjectUnitId);

                $resultArr = ['competency' => $arrAssessmentCompetency, 'list' => $arrUnitAssessmentList];
                echo json_encode($resultArr);
                break;
        }
        exit;
    }

    private function getCourseTrainingPlanList($collegeId, $studentId, $courseId)
    {

        $arrUnitTypeList = Config::get('constants.arrSubjectType');
        $arrDeliveryMode = Config::get('constants.arrDeliveryMode');

        $objStudentCourse = new StudentCourse;
        $studentCourseInfo = $objStudentCourse->getCourseCampusAndDateRange($studentId, $courseId);
        $objCourseTraining = new CourseTraining;
        $arrTrainingPlanList = $objCourseTraining->getCourseTrainingPlanArr($collegeId, $studentId, $courseId);
        if ($arrTrainingPlanList->count() > 0) {

            $arrSemesterList = $this->getSemesterList($courseId, (count($studentCourseInfo) > 0) ? $studentCourseInfo['start_date'] : '');

            reset($arrSemesterList);
            $firstArrSemesterKey = key($arrSemesterList);
            $arrTermList = $this->getTermList($collegeId, $firstArrSemesterKey);

            $objCourseTemplate = new CourseTemplate;
            $templateList = $objCourseTemplate->getCourseWiseTemplateList($collegeId, $courseId);

            $arrSubjectUnitList = $objCourseTraining->getUnitList($collegeId, $courseId, $studentId);

            $objStaff = new Staff;
            $arrTrainer = $objStaff->getTeacherList($collegeId);

            $objStudentTraining = new StudentTraining;
            $arrExistedModuleList = $objStudentTraining->getStudentTrainingPlanArr($collegeId, $studentId, $courseId);

            // print_r($arrExistedModuleList);exit;
            $result = ['duration' => (count($studentCourseInfo) > 0) ? $studentCourseInfo['duration'] : '',
                'campus' => (count($studentCourseInfo) > 0) ? $studentCourseInfo['name'] : '',
                'trainingList' => $arrTrainingPlanList,
                'semesterList' => $arrSemesterList,
                'termList' => $arrTermList,
                'templateList' => $templateList,
                'subjectUnitList' => $arrSubjectUnitList,
                'startDate' => (count($studentCourseInfo) > 0) ? $studentCourseInfo['start_date'] : '',
                'finishDate' => (count($studentCourseInfo) > 0) ? $studentCourseInfo['finish_date'] : '',
                'unitType' => $arrUnitTypeList,
                'deliveryMode' => $arrDeliveryMode,
                'trainer' => $arrTrainer,
                'existedModule' => $arrExistedModuleList];
        } else {

            $result = ['duration' => (count($studentCourseInfo) > 0) ? $studentCourseInfo['duration'] : '',
                'campus' => (count($studentCourseInfo) > 0) ? $studentCourseInfo['name'] : '',
                'trainingList' => $arrTrainingPlanList,
                'existedModule' => ''];
        }

        echo json_encode($result);
        exit;
    }

    private function getSemesterList($courseId, $startDate)
    {

        $startYear = date('Y', strtotime($startDate));

        $objCourseCalender = new CourseCalendar;
        $calendarType = $objCourseCalender->_getCalenderData($courseId, $startYear);
        $courseTypeId = $objCourseCalender->getCourseTypeId($courseId);

        //        for ($i = 0; $i < count($arrCourseCalender); $i++) {
        //            $calendarType[$i] = $arrCourseCalender[$i]['calendar_type'];
        //        }

        $objSemester = new Semester;
        $arrSemesterList = $objSemester->_getSemesterData($courseTypeId, $calendarType, $startYear);

        $semester = [];
        if ($arrSemesterList->count() > 0) {
            foreach ($arrSemesterList as $sem) {
                $semester[$sem->id] = $sem->semester_name;
            }
        } else {
            $semester[''] = '- - No Semester Found - -';
        }

        return $semester;
    }

    public function getTermList($collegeId, $semesterId)
    {

        $objRtoSemesterDivision = new SemesterDivision;
        $termList = $objRtoSemesterDivision->_getTermBySemester($semesterId, $collegeId);

        $result = [];
        if ($termList->count() > 0) {
            foreach ($termList as $term) {
                $result[$term->term] = $term->term;
            }
        } else {
            $result[''] = '- - No Trem Found - -';
        }

        return $result;
    }

    public function addTraineeshipActivityLog($trainingPlanId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;
        $perPage = Config::get('constants.pagination.perPage');
        $arrActivityLogType = Config::get('constants.traineeActivityLogType');

        $objStudentTraining = new StudentTraining;
        $trainingPlanInfo = $objStudentTraining->getStudentTrainingPlanInfo($collegeId, $trainingPlanId);

        $studentId = $trainingPlanInfo[0]->student_id;

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objActivityLog = new TrainieeshipActivityLog;
        $arrActivityLogList = $objActivityLog->getActivityLogList($collegeId, $trainingPlanId, $perPage);

        if ($request->isMethod('post')) {

            $validator = Validator::make($request->all(), ['supervisor' => 'required',
                'visit_date' => 'required',
                'visit_time' => 'required',
                'type' => 'required',
                'note' => 'required']);

            if ($validator->fails()) {
                return redirect(route('traineeship-activity-log', ['id' => $trainingPlanId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objActivityLog = new TrainieeshipActivityLog;
            $objActivityLog->saveActivityLogInfo($collegeId, $trainingPlanId, $request, $userId);

            $request->session()->flash('session_success', 'Traineeship Activity Log Add Successfully.');

            return redirect(route('traineeship-activity-log', ['id' => $trainingPlanId]));
        }
        $data['header'] = [
            'title' => 'Activity Log for Traineeship ',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Trineeship Visits' => route('trineeship-visits'),
                'Add Trineeship Activity Log' => '',
            ]];
        $data['pagetitle'] = 'Activity Log for Traineeship';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['courseTraining.js'];
        $data['funinit'] = ['courseTraining.initActivityInfo()'];

        $data['studentId'] = $studentId;
        $data['studentDetail'] = $arrStudentDetail;
        $data['trainingPlanInfo'] = $trainingPlanInfo[0];
        $data['arrActivityLogType'] = $arrActivityLogType;
        $data['arrActivityLogList'] = $arrActivityLogList;
        $data['trainingPlanId'] = $trainingPlanId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student_training.activity-log', $data);
    }

    public function editTraineeshipActivityLog($trainingPlanId, $activityLogId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;
        $arrActivityLogType = Config::get('constants.traineeActivityLogType');

        $objStudentTraining = new StudentTraining;
        $trainingPlanInfo = $objStudentTraining->getStudentTrainingPlanInfo($collegeId, $trainingPlanId);

        $studentId = $trainingPlanInfo[0]->student_id;

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objActivityLog = new TrainieeshipActivityLog;
        $arrActivityLogInfo = $objActivityLog->getActivityLogInfo($collegeId, $trainingPlanId, $activityLogId);
        //        $arrActivityLogList = $objActivityLog->getActivityLogList($collegeId, $trainingPlanId, $perPage);

        if ($request->isMethod('post')) {

            $validator = Validator::make($request->all(), ['supervisor' => 'required',
                'visit_date' => 'required',
                'visit_time' => 'required',
                'type' => 'required',
                'note' => 'required']);

            if ($validator->fails()) {
                return redirect(route('edit-traineeship-activity-log', ['training_plan_id' => $trainingPlanId, 'id' => $activityLogId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objActivityLog = new TrainieeshipActivityLog;
            $result = $objActivityLog->updateActivityLogInfo($activityLogId, $request, $userId);
            if ($result) {
                $request->session()->flash('session_success', 'Traineeship Activity Log Update Successfully.');

                return redirect(route('traineeship-activity-log', ['id' => $trainingPlanId]));
            } else {
                $request->session()->flash('session_error', 'Something will be Wrong. Please Try again.');

                return redirect(route('edit-traineeship-activity-log', ['training_plan_id' => $trainingPlanId, 'id' => $activityLogId]));
            }
        }
        $data['header'] = [
            'title' => 'Edit Activity Log for Traineeship ',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Trineeship Visits' => route('trineeship-visits'),
                'Edit Trineeship Activity Log' => '',
            ]];
        $data['pagetitle'] = 'Edit Activity Log for Traineeship';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['courseTraining.js'];
        $data['funinit'] = ['courseTraining.initActivityInfo()'];

        $data['studentId'] = $studentId;
        $data['studentDetail'] = $arrStudentDetail;
        $data['trainingPlanInfo'] = $trainingPlanInfo[0];
        $data['arrActivityLogType'] = $arrActivityLogType;
        $data['arrActivityLogInfo'] = $arrActivityLogInfo[0];
        $data['trainingPlanId'] = $trainingPlanId;
        $data['mainmenu'] = 'clients';

        return view('frontend.student_training.activity-log', $data);
    }

    public function deleteTraineeshipActivityLog(Request $request)
    {

        $activityLogId = $request->trainingPlanId;
        $primaryId = $request->id;
        $collegeId = Auth::user()->college_id;

        $objActivityLog = new TrainieeshipActivityLog;
        $result = $objActivityLog->deleteActivityLogInfo($collegeId, $activityLogId);

        if ($result) {
            $request->session()->flash('session_success', 'Traineeship Activity Log Delete Successfully.');
        } else {
            $request->session()->flash('session_error', 'Something will be Wrong. Please Try again.');
        }

        return redirect(route('traineeship-activity-log', ['id' => $primaryId]));
    }

    public function traineeshipUnitAssessment($trainingPlanId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $arrAssessmentCompetency = Config::get('constants.arrAssessmentCompetency');

        $objStudentTraining = new StudentTraining;
        $trainingPlanInfo = $objStudentTraining->getStudentTrainingPlanInfo($collegeId, $trainingPlanId);

        $studentId = $trainingPlanInfo[0]->student_id;
        $subjectUnitId = $trainingPlanInfo[0]->subject_unit_id;

        $objStudents = new Students;
        $arrStudentDetail = $objStudents->getStudents($studentId);

        $objUnitModule = new UnitModule;
        $arrUnitInfo = $objUnitModule->getUnitModuleInfo($collegeId, $subjectUnitId);

        $arrAssessmentTaskList = $objStudentTraining->getAssessmentTaskByUnit1($collegeId, $trainingPlanId);

        $objUnitAssessment = new TraineeUnitAssessment;
        $arrUnitAssessmentList = $objUnitAssessment->getUnitAssessmentList($collegeId, $studentId, $subjectUnitId);

        // echo "<pre>";print_r($arrAssessmentTaskList);exit;

        if ($request->isMethod('post')) {

            $validator = Validator::make($request->all(), ['task_name' => 'required',
                'unit' => 'required',
                'description' => 'required',
                'due_date' => 'required',
                'due_time' => 'required']);

            if ($validator->fails()) {
                return redirect(route('traineeship-unit-assessment', ['id' => $trainingPlanId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objUnitAssessment = new TraineeUnitAssessment;
            $objUnitAssessment->saveUnitAssessmentInfo($collegeId, $request);

            $request->session()->flash('session_success', 'Traineeship Unit Assessment Add Successfully.');

            return redirect(route('traineeship-unit-assessment', ['id' => $trainingPlanId]));
        }
        $data['header'] = [
            'title' => 'Traineeship Unit Assessment',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Student Profile' => route('student-profile', ['id' => $studentId]),
                'Student Training Plan' => route('student-profile', ['id' => $studentId]),
                'Add Traineeship Unit Assessment' => '',
            ]];

        $data['pagetitle'] = 'Unit Assessment for Traineeship';
        $data['plugincss'] = [];
        $data['css'] = [''];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['courseTraining.js'];
        $data['funinit'] = ['courseTraining.initAssessmentInfo()'];

        $data['studentId'] = $studentId;
        $data['studentDetail'] = $arrStudentDetail;
        $data['trainingPlanInfo'] = $trainingPlanInfo[0];
        $data['arrUnitInfo'] = $arrUnitInfo[0];
        $data['arrAssessmentCompetency'] = $arrAssessmentCompetency;
        $data['arrAssessmentTaskList'] = $arrAssessmentTaskList;
        $data['arrUnitAssessmentList'] = $arrUnitAssessmentList;
        $data['mainmenu'] = 'clients';

        return view('frontend.student_training.unit-assessment', $data);
    }

    public function deleteTraineeshipUnitAssessment($trainingPlanId, $unitAssessmentId, Request $request)
    {

        // $collegeId = Auth::user()->college_id;

        $objUnitAssessment = new TraineeUnitAssessment;
        $result = $objUnitAssessment->deleteUnitAssessmentInfo($unitAssessmentId);

        if ($result) {
            $request->session()->flash('session_success', 'Traineeship Unit Assessment Delete Successfully.');
        } else {
            $request->session()->flash('session_error', 'Something will be Wrong. Please Try again.');
        }

        return redirect(route('traineeship-unit-assessment', ['id' => $trainingPlanId]));
    }

    public function generateTrainingPlanDOC10($studentId, Request $request)
    {
        // echo "hi";exit;

        $project_id = 3;
        $questiondetails = DB::table('rto_agents')->where('college_id', '=', $project_id)->get();

        $headers = [
            //            "Content-type" => "text/html",
            'Content-type' => 'text',
            'Content-Disposition' => 'attachment;Filename=report.doc',
        ];
        $content = "<html>
        <head>
        <meta charset='utf-8'>
        </head>
        <body>
            <p>
                <table border='1'>
                <tr>
                    <td><b>Question Number</b></td>
                    <td><b>User ID</b></td>
                    <td><b>Answer</b></td>
                    <td><b>Project ID</b></td>
                    <td><b>Project Status</b></td>
                </tr>
               <% for(var i=0; i< count($questiondetails); i++) {  %> 
                <tr>
                    <td> ".' ddddddddd '.'</td>
                    <td> '.' ddddddddd '.'</td>
                    <td> '.' ddddddddd '.'</td>
                    <td> '.' ddddddddd '.'</td>
                </tr>
               <%   } %>
                </table>
            </p>
        </body>
        </html>';

        return Response::make($content, 200, $headers);
    }

    public function generateTrainingPlanDOC($studentId, Request $request)
    {
        // echo "hi";exit;

        $phpWord = new \PhpOffice\PhpWord\PhpWord;
        $data['data'] = [];
        $section = $phpWord->addSection();
        $description1 = view('welcome', $data);

        $section->addText($description1);

        $section->addText('PLEASE READ BEFORE COMPLETING THIS TRAINING PLAN', ['name' => 'Tahoma', 'size' => 12, 'bold' => true]);
        $description1 = 'This Training Plan is a document to be developed by the Supervising Registered Training Organisation (SRTO), 
                the employer and the apprentice/trainee. 
                It outlines who provides the training and assessment and how, when and where it occurs. 
                It should be customised within the qualification packaging guidelines to accommodate 
                the individual needs of the apprentice/trainee and the workplace, and to provide enough details to assist employers to meet their obligations under the Training Contract.
                This Training Plan is a working document to be used for the duration of the Training Contract.
                Copies, including updates, should be accessible at the workplace as a reference and to monitor progress ';
        $section->addText($description1, ['name' => 'Tahoma', 'color' => '1B2232', 'size' => 10, 'bold' => false, 'border' => true]);

        $section->addText('This Training Plan comprises the following parts', ['name' => 'Tahoma', 'size' => 12, 'bold' => true]);
        $description2 = 'Contact Details - records relevant details of the Training Contract and the parties responsible. It outlines proposed overall time lines for both the structured training and the Training Contract.
                        Training Plan - includes the selected units of competency as discussed during the sign up and is used to record details of the proposed training and assessment arrangements and details of assessment as competence is achieved. 
                        It should be reviewed and updated as required.
                        The contact training time will be established upon negotiation between the SRTO and the employer.';
        $section->addText($description2, ['name' => 'Tahoma', 'color' => '1B2232', 'size' => 10, 'bold' => false, 'border' => true]);

        $section->addText('This Training Plan comprises the following parts', ['name' => 'Tahoma', 'size' => 12, 'bold' => true]);

        $section->addText('Competency requires not just the possession of workplace related knowledge and skills but the demonstrated ability to apply specified knowledge and skills consistently over time in a sufficient range of work contexts.                                                                                                                                                                    
                            This Training Plan is designed to focus on the apprentice gaining the knowledge and skills to the standard performance required by industry as detailed in the qualification.                                                                                                                                                                                                                                      Competency based training and completion allows apprentices to move through their apprenticeship as they attain competencies rather than by serving time
                            ', ['name' => 'Tahoma', 'color' => '1B2232', 'size' => 10, 'bold' => false, 'border' => false]);
        $section->addText('RTO agrees to:', ['name' => 'Tahoma', 'color' => '1B2232', 'size' => 12, 'bold' => true, 'border' => true]);
        $content = '  fdfdfdfdff';
        $section->addText($content, ['name' => 'Tahoma', 'color' => '1B2232', 'size' => 10, 'bold' => false, 'border' => true]);

        $fontStyle = new \PhpOffice\PhpWord\Style\Font;
        $fontStyle->setBold(false);
        $fontStyle->setName('Tahoma');
        $fontStyle->setSize(13);
        $myTextElement = $section->addText('');
        $myTextElement->setFontStyle($fontStyle);

        // Saving the document as OOXML file...
        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        //       $objWriter =  \PhpOffice\PhpWord\Shared\Html::addHtml($section, $html);
        $objWriter->save('StudentTrainingPlan.docx');

        return response()->download(public_path('StudentTrainingPlan.docx'))->deleteFileAfterSend(true);
    }

    public function generateTrainingPlanPDF($studentId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        // $data['logoPath'] = Config::get('constants.displayCollegeLogo');
        // $data['logoPath'] = Config::get('constants.displayCollegeLogoPDF');
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $data['logoPath'] = $destinationPath['default'];

        $objRtoCollegeDetails = new CollegeDetails;
        $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

        $data['objCollegeDetails'] = $objCollegeDetails;
        $data['studentId'] = $studentId;

        $pdf = App::make('dompdf.wrapper');

        // $pdf->loadHTML('<h1>Testaa</h1>');
        // load from other pages use object or array by comma like (pdf-view,$user)
        $pdf->loadView('frontend.student_training.training-plan-pdf', $data);

        // open in browser
        // return $pdf->stream();exit;
        return $pdf->download('studentTrainingPlan.pdf');
    }

    public function generateTrainingPlanXLS($studentId, $courseId, Request $request)
    {
        // echo $studentId .'-----------'. $courseId;exit;

        $collegeId = Auth::user()->college_id;
        $objCourseTraining = new CourseTraining;
        $arrTrainingPlanList = $objCourseTraining->getCourseTrainingPlanForXLS($collegeId, $studentId, $courseId);

        $objStudentTraining = new StudentTraining;
        $arrExistedModuleList = $objStudentTraining->getStudentTrainingPlanForXLS($collegeId, $studentId, $courseId);

        $objActivityLog = new TrainieeshipActivityLog;
        $arrActivityLogList = $objActivityLog->getActivityLogListforXLS($collegeId, $studentId, $courseId);

        //        echo "<pre>";
        //        print_r($arrActivityLogList);
        //        exit;

        $directoryPath = '/uploads/frontend/StudentTrainingPlan/'.date('Y-m-d').'/';

        $sheet = Excel::create('organisation-details', function ($excel) use ($arrTrainingPlanList, $arrExistedModuleList, $arrActivityLogList) {

            $header1 = ['StudentId',
                'Name',
                'CourseID',
                'CourseName',
                'CourseAttempt',
                'Training Contract ID',
                'Contract ID',
                'Purchasing Contract Schedule Id',
                'Contract Date',
                'Employer',
                'Apprenticeship ID',
                'Apprenticeship Name',
                'ContractType',
                'Notes',
                'State',
                'Status'];

            $header2 = ['SubjectID',
                'SubjectName',
                'UnitId',
                'UnitName',
                'Trainer',
                'UnitType',
                'Start Date',
                'Finish Date'];

            $header3 = ['SubjectID',
                'SubjectName',
                'UnitId',
                'UnitName',
                'Trainer',
                'Supervisor',
                'Visit Date & Time',
                'Notes',
                'Duration (Minutes)',
                'Type',
                'Completed'];

            $excel->sheet('organisation-details', function ($sheet) use ($header1, $header2, $header3, $arrTrainingPlanList, $arrExistedModuleList, $arrActivityLogList) {

                /* Student Training Plan */
                $title1 = [['Student Training Plan']];
                $sheet->fromArray($title1, null, 'A1', false, false);
                $sheet->mergeCells('A1:P1');
                //                                                $sheet->cells('A1:p1', function($cells) {
                //                                                    $cells->setAlignment('center');
                //                                                });

                $count = 2;
                $sheet->prependRow($header1);
                for ($i = 0; $i < count($arrTrainingPlanList); $i++) {
                    $sheet->row($count, [
                        $arrTrainingPlanList[$i]->generated_stud_id,
                        $arrTrainingPlanList[$i]->student_name,
                        $arrTrainingPlanList[$i]->course_code,
                        $arrTrainingPlanList[$i]->course_name,
                        $arrTrainingPlanList[$i]->course_attempt,
                        $arrTrainingPlanList[$i]->training_contract_id,
                        $arrTrainingPlanList[$i]->contract_code,
                        $arrTrainingPlanList[$i]->contract_schedule_id,
                        $arrTrainingPlanList[$i]->contract_date,
                        $arrTrainingPlanList[$i]->employer_name,
                        $arrTrainingPlanList[$i]->apprenticeship_id,
                        $arrTrainingPlanList[$i]->apprenticeship_name,
                        $arrTrainingPlanList[$i]->contract_type,
                        $arrTrainingPlanList[$i]->note,
                        $arrTrainingPlanList[$i]->state,
                        $arrTrainingPlanList[$i]->status,
                    ]);
                    $count++;
                }

                /* Modules Information for Student Training Plan */
                $count = $count + 5;
                $title2 = ['Modules Information for Student Training Plan'];
                $sheet->row($count, ($title2));
                $sheet->mergeCells("A$count:P$count");

                $count = $count + 1;
                $sheet->row($count, ($header2));
                $count = $count + 1;

                for ($i = 0; $i < count($arrExistedModuleList); $i++) {
                    $sheet->row($count, [
                        $arrExistedModuleList[$i]->subject_code,
                        $arrExistedModuleList[$i]->subject_name,
                        $arrExistedModuleList[$i]->unit_code,
                        $arrExistedModuleList[$i]->unit_name,
                        $arrExistedModuleList[$i]->trainer,
                        $arrExistedModuleList[$i]->unit_type,
                        $arrExistedModuleList[$i]->start_date,
                        $arrExistedModuleList[$i]->finish_date,
                    ]);
                    $count++;
                }

                /* Visiting Activity Log for Training Plan */
                $count = $count + 5;
                $title3 = ['Visiting Activity Log for Training Plan'];
                $sheet->row($count, ($title3));
                $sheet->mergeCells("A$count:P$count");

                $count = $count + 1;
                $sheet->row($count, ($header3));
                $count = $count + 1;

                for ($i = 0; $i < count($arrActivityLogList); $i++) {
                    $sheet->row($count, [
                        $arrActivityLogList[$i]->subject_code,
                        $arrActivityLogList[$i]->subject_name,
                        $arrActivityLogList[$i]->unit_code,
                        $arrActivityLogList[$i]->unit_name,
                        $arrActivityLogList[$i]->trainer,
                        $arrActivityLogList[$i]->supervisor,
                        $arrActivityLogList[$i]->visit_date_time,
                        $arrActivityLogList[$i]->note,
                        $arrActivityLogList[$i]->duration,
                        $arrActivityLogList[$i]->type,
                        $arrActivityLogList[$i]->is_completed,
                    ]);
                    $count++;
                }
            });
        });
        $sheet->store('xlsx', public_path().$directoryPath);
        $excel = App::make('excel');
        $sheet->export('xls');
    }

    public function downloadFile(Request $request)
    {

        $primaryId = base64_decode($request->id);
        $filePath = Config::get('constants.uploadFilePath.TraineeshipActivity');
        $destinationPath = Helpers::changeRootPath($filePath);

        $objTrainieeLog = TrainieeshipActivityLog::find($primaryId);
        $fileName = $objTrainieeLog->file_name;
        $originalName = substr($objTrainieeLog->file_name, 10);
        $path = $destinationPath['default'].$fileName;

        return response()->download($path, $originalName);

        // return Response::download($path . $file);
    }
}
