<template>
    <Dialog
        v-if="visible"
        title="Import Errors"
        :width="600"
        @close="closeModal"
        :class="'k-modal-window'"
        :dialog-class="'tw-dialog custom-modal-wrapper'"
        append-to="body"
    >
        <div v-if="hasErrors" class="space-y-3 p-4">
            <div v-if="isStringError" class="rounded-md border border-red-200 bg-red-50 p-3">
                <p class="text-sm text-red-600">{{ actualErrors }}</p>
            </div>
            <div v-else-if="isObjectError" class="space-y-2">
                <div
                    v-for="(messages, field) in actualErrors"
                    :key="field"
                    class="rounded-md border border-red-200 bg-red-50 p-3"
                >
                    <strong class="text-sm font-medium capitalize text-red-700"
                        >{{ formatFieldName(field) }}:</strong
                    >
                    <ul
                        v-if="Array.isArray(messages)"
                        class="ml-2 mt-1 list-inside list-disc space-y-1"
                    >
                        <li
                            v-for="(message, index) in messages"
                            :key="index"
                            class="text-sm text-red-600"
                        >
                            {{ message }}
                        </li>
                    </ul>
                    <p v-else class="mt-1 text-sm text-red-600">{{ messages }}</p>
                </div>
            </div>
            <div v-else-if="isArrayError" class="space-y-2">
                <div
                    v-for="(errorItem, index) in actualErrors"
                    :key="index"
                    class="rounded-md border border-red-200 bg-red-50 p-3"
                >
                    <!-- If errorItem is an object, display its fields -->
                    <div v-if="typeof errorItem === 'object' && errorItem !== null">
                        <div v-for="(msg, field) in errorItem" :key="field">
                            <strong class="text-sm font-medium capitalize text-red-700"
                                >{{ formatFieldName(field) }}:</strong
                            >
                            <span class="ml-1 text-sm text-red-600">{{ msg }}</span>
                        </div>
                    </div>
                    <!-- Otherwise, display as string -->
                    <p v-else class="text-sm text-red-600">{{ errorItem }}</p>
                </div>
            </div>
        </div>
        <div v-else class="p-4 text-gray-600">No errors to display.</div>
        <DialogActionsBar>
            <div class="flex justify-end space-x-2">
                <Button variant="secondary" @click="closeModal"> Close </Button>
            </div>
        </DialogActionsBar>
    </Dialog>
</template>

<script>
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import { computed } from 'vue';

export default {
    components: {
        Dialog,
        DialogActionsBar,
        Button,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        errors: {
            type: [String, Object, Array],
            default: null,
        },
    },
    emits: ['update:visible'],
    setup(props, { emit }) {
        const closeModal = () => {
            emit('update:visible', false);
        };

        const actualErrors = computed(() => {
            if (typeof props.errors === 'string') {
                try {
                    const parsed = JSON.parse(props.errors);
                    if (typeof parsed === 'object' && parsed !== null) {
                        return parsed;
                    }
                    return props.errors; // Not an object/array after parsing, or not meant to be JSON
                } catch (e) {
                    return props.errors; // Not valid JSON
                }
            }
            return props.errors;
        });

        const finalErrorType = computed(() => {
            const err = actualErrors.value;
            if (typeof err === 'string') return 'string';
            if (Array.isArray(err)) return 'array';
            if (typeof err === 'object' && err !== null) return 'object';
            return 'none';
        });

        const hasErrors = computed(() => {
            const err = actualErrors.value;
            const type = finalErrorType.value;

            if (type === 'string') {
                return err.trim() !== '';
            }
            if (type === 'array') {
                return err.length > 0;
            }
            if (type === 'object') {
                return Object.keys(err).length > 0;
            }
            return false;
        });

        const isStringError = computed(() => finalErrorType.value === 'string');
        const isObjectError = computed(() => finalErrorType.value === 'object');
        const isArrayError = computed(() => finalErrorType.value === 'array');

        const formatFieldName = (fieldName) => {
            // Replace underscores and hyphens with spaces, then capitalize each word
            return fieldName.replace(/[_-]/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());
        };

        return {
            closeModal,
            actualErrors, // Expose for template
            hasErrors,
            isStringError,
            isObjectError,
            isArrayError,
            formatFieldName,
        };
    },
};
</script>
