<template>
    <div
        class="fixed inset-x-0 bottom-0 z-50 w-full bg-white px-8 py-4 shadow-2xl duration-300 animate-in slide-in-from-bottom border-t border-gray-200"
    >
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <input
                    type="checkbox"
                    :checked="true"
                    @change="$emit('cancel')"
                    class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="text-sm font-medium text-gray-700">
                    {{ selectedCount }} {{ selectedCount === 1 ? 'import' : 'imports' }} selected
                </span>
            </div>
            
            <div class="flex items-center gap-4">
                <Button
                    variant="danger"
                    class="h-9"
                    @click="$emit('bulk-delete')"
                    :disabled="loading"
                >
                    <icon :name="'delete'" :width="16" :height="16" />
                    <span>Delete Selected</span>
                </Button>
            </div>
            
            <div class="flex items-center">
                <button
                    @click="$emit('cancel')"
                    class="p-2 text-gray-400 hover:text-gray-600"
                    title="Cancel Selection"
                >
                    <icon :name="'cross'" :width="20" :height="20" />
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import Button from '@spa/components/Buttons/Button.vue';

export default {
    components: {
        Button,
    },
    props: {
        selectedCount: {
            type: Number,
            required: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['bulk-delete', 'cancel'],
};
</script>

<style scoped>
.animate-in {
    animation: slideInFromBottom 0.3s ease-out;
}

.slide-in-from-bottom {
    transform: translateY(100%);
}

@keyframes slideInFromBottom {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}
</style>
