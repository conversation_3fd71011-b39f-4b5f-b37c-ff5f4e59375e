.k-form-layout {
    .k-form-field.tw-switch-field {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        align-items: center;
    }
}

#gteStepsWizard {
    #gteStepsWizard-0 {
        .k-wizard-content {
            position: relative;
            &::before {
                content: '';
                position: absolute;
                height: 36rem;
                width: 1px;
                border-left: 1px dashed var(--color-primary-blue-200);
                left: 2rem;
                top: 6rem;
                z-index: 1;
            }
        }
    }
}

#userDetailForm {
    .k-form-buttons {
        position: absolute;
        width: 100%;
        bottom: 0;
        background: var(--color-gray-50);
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        padding: 0.75rem 1.5rem;
        left: 0;
    }
}

.student-list {
    .tagify__dropdown__item {
        padding: 0.5rem 1rem;
    }
}

#generatePaymentScheduleSteps {
    .k-step-list-vertical .k-step {
        padding: 0.5rem 0.25rem;
        min-height: fit-content;
        &.k-step-current {
            background: var(--color-primary-blue-50);
            border-radius: 4px;
            border: 1px solid var(--color-primary-blue-500);
            .k-step-label {
                color: var(--color-primary-blue-500);
            }
        }
        .k-step-indicator > .k-step-indicator-text {
            opacity: 0;
            visibility: hidden;
        }
    }
    .k-step-list-vertical ~ .k-progressbar {
        height: calc(100% - 1rem) !important;
        margin-top: -150px;
        left: calc((2rem + 2 * 1px + 2 * 5px) / 2);
    }

    &.k-stepper .k-step-indicator {
        width: 2rem;
        height: 2rem;
        &::after {
            box-shadow: inset 0 0 0 8px #ffffff;
            top: calc(-1 * calc(1px + 1px));
            right: calc(-1 * calc(1px + 1px));
            bottom: calc(-1 * calc(1px + 1px));
            left: calc(-1 * calc(1px + 1px));
        }
    }
}

#addScholarshipInfoModal,
#editScholarshipInfoModal {
    .k-form-buttons {
        display: none !important;
    }
}

.course-skeleton {
    position: relative;
}

.header_course_list {
    .k-dropdown-wrap {
        padding-block: 0;
        // background-color: white;
        height: 1.875rem;
        align-items: center;
    }
}

.profile__header-dropdown {
    .k-dropdown-wrap {
        padding-block: 0;
        background-color: white;
        height: 1.875rem;
        align-items: center;
    }
}

#attendanceGrid {
    .k-pager-wrap {
        .k-pager-sizes {
            order: -1;
            visibility: hidden;
            .k-dropdown {
                visibility: visible;
            }
        }
        .k-link.k-pager-nav.k-pager-first {
            margin-left: auto;
        }
    }
}

.attendance-li-detail {
    .profile__header-dropdown .k-dropdown-wrap .k-input {
        font-size: 0.75rem;
    }
}

.tw-summary-grid {
    & &__content {
        @media (max-width: 1024px) {
            width: 100% !important;
        }
    }

    & &__sidebar {
        &.active {
            @media (max-width: 1024px) {
                display: block;
                width: 80% !important;
                max-width: 400px;
                z-index: 1;
                overflow: visible;
                position: fixed !important;
                top: 0 !important;
                left: 0;
                height: 100% !important;
            }
            &::after {
                content: '';
                position: fixed;
                background: rgba(0, 0, 0, 0.4);
                inset: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
            }
        }
    }
}

.tagify__input {
    margin: 0;
}

.viewSentMailMessage {
    &:hover {
        background-color: var(--color-bluegray-100);
        border-radius: 6px;
    }
}

.tw-h-timeline {
    width: 100%;
    .horizontal-timeline {
        max-width: 1050px;
        overflow: hidden;
        margin: 0 !important;
        opacity: 1;
    }

    .horizontal-timeline .touch-enabled {
        cursor: default;
    }
    .horizontal-timeline .filling-line {
        background-color: var(--color-primary-blue-500);
    }

    .horizontal-timeline .events {
        background-color: var(--color-gray-200);
    }

    .horizontal-timeline .events a {
        max-width: 200px;
        font-size: 0.75rem;
        color: var(--color-gray-500);
        &:hover {
            text-decoration: none;
        }
    }

    #summaryTimeline.horizontal-timeline .events a {
        max-width: 160px;
    }

    .horizontal-timeline .events-content li {
        background-color: white;
        padding: 4px;
    }

    .horizontal-timeline .events-wrapper {
        height: 128px;
    }

    .horizontal-timeline .events {
        height: 2px;
        color: var(--color-gray-500);
    }

    .timeline-navigation {
        top: 14px;
    }

    .horizontal-timeline .events a.older-event::after,
    .horizontal-timeline .events a.selected::after {
        background-color: var(--color-primary-blue-500);
        border-color: var(--color-primary-blue-500);
    }

    .horizontal-timeline .events a::after {
        width: 1.875rem;
        height: 1.875rem;
        bottom: -15px;
        font-family: WebComponentsIcons, monospace;
        content: '\e118';
        position: absolute;
        color: #fff;
        font-size: 1rem;
        line-height: 1.875rem;
    }

    .horizontal-timeline .events {
        bottom: 22%;
        min-width: 200px;
    }

    .horizontal-timeline .events a::after {
        background-color: white;
        border: 2px solid var(--color-gray-200);
    }
    .timeline {
        padding-left: 0;
    }

    .k-icon {
        display: inline-flex;
        background-color: var(--color-gray-200);
        border-radius: 50%;
        color: var(--color-gray-400);
        width: 2rem;
        height: 2rem;
        margin-top: 0.35rem;
        &.inactive {
            opacity: 0.7;
        }
    }

    .k-card-callout.k-callout-n {
        display: none;
        position: absolute;
        width: 20px;
        height: 20px;
        border-width: 1px 0 0 1px;
        border-style: solid;
        margin: 0;
        box-shadow: none;
        top: 4px;
    }

    .horizontal-timeline .events-content {
        margin: 0.25rem 0;
    }

    .horizontal-timeline .events-wrapper {
        overflow: visible;
    }

    a::after {
        pointer-events: none; /* ensures clicks pass through */
    }

    .horizontal-timeline .events a {
        display: flex;
        flex-direction: column;
        span {
            display: block;
            text-align: ceneter;
        }
    }
}

.k-timeline-horizontal .k-timeline-track-wrap .k-timeline-track {
    height: 110px;
    overflow: hidden;
}
