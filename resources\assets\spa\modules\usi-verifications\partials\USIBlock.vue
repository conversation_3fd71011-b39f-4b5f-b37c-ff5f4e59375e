<script setup>
import { computed, defineProps, ref } from 'vue';
import Button from '@spa/components/Buttons/Button.vue';
import { Button as KButton } from '@progress/kendo-vue-buttons';
import InputGroup from '@spa/components/InputGroup.vue';
import { Input } from '@progress/kendo-vue-inputs';
import Checkbox from '@spa/components/Checkbox.vue';
import Icon from '@spa/components/Icons.vue';
import USIBlockLine from '@spa/modules/usi-verifications/partials/USIBlockLine.vue';
const props = defineProps({
    modelValue: {},
    saveNode: {
        type: Function,
        default: () => {},
    },
    verifyNode: {
        type: Function,
        default: () => {},
    },
});
const emit = defineEmits(['update:modelValue', 'unlinkNode']);
const vModel = computed({
    get() {
        return props.modelValue ?? {};
    },
    set(value) {
        emit('update:modelValue', value);
    },
});
const vResult = computed({
    get() {
        return vModel.value?.result;
    },
    set(value) {
        vModel.value.result = value;
    },
});
const loading = ref(false);
const currentAction = ref(null);
</script>
<template>
    <div
        class="flex max-w-3xl flex-wrap rounded-lg border-2 border-solid border-gray-200 bg-white p-2 text-sm text-gray-500"
    >
        <div class="flex w-full items-center justify-between p-1">
            <div>
                <KButton
                    :loading="loading"
                    :disabled="loading"
                    :variant="'danger'"
                    size="small"
                    rounded="full"
                    class="h-6 w-6 p-0 hover:!bg-gray-50"
                    @click="
                        () => {
                            emit('unlinkNode', vModel);
                        }
                    "
                >
                    <icon name="cancel" :width="15" :height="15" :fill="'#9CA3AF'" />
                </KButton>
                #{{ vModel.id }}
            </div>
            <div>
                <Button
                    v-if="!vResult?.status && (currentAction === 'verifyNode' || !loading)"
                    class="ml-1 text-xs"
                    size="xs"
                    :loading="loading"
                    loading-text="Verifying..."
                    :disabled="loading"
                    :variant="'success'"
                    @click="
                        async () => {
                            loading = true;
                            currentAction = 'verifyNode';
                            vResult = await verifyNode(vModel);
                            loading = false;
                        }
                    "
                >
                    Verify USI
                </Button>
                <Button
                    class="ml-1 text-xs"
                    size="xs"
                    :variant="'secondary'"
                    v-if="vResult"
                    @click="() => (vResult = null)"
                >
                    Edit
                </Button>
                <Button
                    v-if="vResult?.isVerified"
                    class="ml-1 text-xs"
                    size="xs"
                    :variant="'primary'"
                    :loading="loading"
                    :disabled="loading"
                    loading-text="Saving..."
                    @click="
                        async () => {
                            loading = true;
                            currentAction = 'saveNode';
                            await saveNode({
                                ...vModel,
                                is_usi_verified: vResult?.isVerified ? 1 : 0,
                            });
                            loading = false;
                        }
                    "
                >
                    Save
                </Button>
            </div>
        </div>
        <template v-if="vResult">
            <div>
                <USIBlockLine
                    :status="vResult.status"
                    label="USI Number"
                    :show-value="false"
                    :trueLabel="'Valid'"
                    :falseLabel="'Invalid'"
                ></USIBlockLine>
                <template v-if="vModel.is_single_name">
                    <USIBlockLine
                        :status="vResult.firstName"
                        label="Single Name"
                        :value="vResult.input.singleName"
                    ></USIBlockLine>
                </template>
                <template v-else>
                    <USIBlockLine
                        :status="vResult.firstName"
                        label="First Name"
                        :value="vResult.input.firstName"
                    ></USIBlockLine>
                    <USIBlockLine
                        :status="vResult.familyName"
                        label="Family Name"
                        :value="vResult.input.familyName"
                    ></USIBlockLine>
                </template>
                <USIBlockLine
                    :status="vResult.dob"
                    label="DOB"
                    :value="vResult.input.dob"
                ></USIBlockLine>
            </div>
        </template>
        <template v-else>
            <div v-if="vModel.is_single_name" class="w-full p-1">
                <input-group label="First Name" invalid-feedback="Please enter a valid first name">
                    <Input v-model="vModel.first_name" placeholder="First Name"></Input>
                </input-group>
            </div>
            <template v-else>
                <div class="w-full p-1 md:w-1/2">
                    <input-group
                        label="First Name"
                        invalid-feedback="Please enter a valid first name"
                    >
                        <Input v-model="vModel.first_name" placeholder="First Name"></Input>
                    </input-group>
                </div>
                <div class="w-full p-1 md:w-1/2">
                    <input-group
                        label="Family Name"
                        invalid-feedback="Please enter a valid family name"
                    >
                        <Input v-model="vModel.family_name" placeholder="Family Name"></Input>
                    </input-group>
                </div>
            </template>
            <div class="w-full p-1 md:w-1/2">
                <input-group label="USI Number" invalid-feedback="Please enter a valid USI number">
                    <Input v-model="vModel.USI" placeholder="USI Number"></Input>
                </input-group>
            </div>
            <div class="w-full p-1 md:w-1/2">
                <input-group
                    label="Date of Birth"
                    invalid-feedback="Please enter a valid date of birth name"
                >
                    <Input v-model="vModel.dob" placeholder="Date of Birth"></Input>
                </input-group>
            </div>
            <div class="w-full p-1">
                <input-group label="Single Name" class="flex items-center gap-2">
                    <Checkbox
                        v-model="vModel.is_single_name"
                        placeholder="Is Single Name"
                        :true-value="1"
                        :false-value="0"
                        class="!mb-1"
                    ></Checkbox>
                </input-group>
            </div>
            <div class="w-full"></div>
        </template>
    </div>
</template>

<style scoped></style>
