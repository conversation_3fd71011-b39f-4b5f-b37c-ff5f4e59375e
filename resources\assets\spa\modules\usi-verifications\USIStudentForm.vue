<template>
    <SidebarDrawer
        :visibleDialog="store.formDialog"
        :hideOnOverlayClick="true"
        :fixedActionBar="true"
        :width="'50%'"
        :style="{ maxWidth: '600px' }"
        :primaryBtnLabel="`Bulk Verify(${store.selected?.length ?? 0})`"
        :secondaryBtnLabel="'Cancel'"
        :isDisabled="store.loading"
        :isSubmitting="store.loading"
        @drawerclose="store.formDialog = false"
        @drawersaved="handelBulkVerify"
    >
        <template #title>
            <div class="text-lg font-medium">Bulk USI Verifications</div>
        </template>
        <template #content>
            <template v-for="(item, index) in store.selected" :key="index">
                <USIBlock
                    v-model="store.selected[index]"
                    @unlink-node="
                        (data) => {
                            store.selected = store.selected.filter((item) => item.id !== data.id);
                        }
                    "
                    :save-node="saveNode"
                    :verify-node="verifyNode"
                ></USIBlock>
            </template>
        </template>
    </SidebarDrawer>
</template>

<script setup>
import { watch } from 'vue';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import { useUsiStudentStore } from '@spa/stores/modules/usi/usiStudentStore.js';
import USIBlock from '@spa/modules/usi-verifications/partials/USIBlock.vue';

const store = useUsiStudentStore();

const saveNode = async (nodeData) => {
    store.formData = { ...nodeData };
    // to avoid loader tabel loader as custom loader is used in USIBlock
    store.enableLoader = false;
    const result = await store.update();
    store.enableLoader = true;
    if (result?.data?.code === 200) {
        store.notifySuccess('USI Updated Successfully');
    }
    return result;
};
const verifyNode = async (nodeData) => {
    return await store.verify(nodeData);
};

const handelBulkVerify = async () => {
    const result = await store.bulkVerify();
    if (!result) return;
    const validations = result?.validations ?? [];
    validations.forEach((validation) => {
        const id = validation?.input?.recordId ? parseInt(validation?.input?.recordId) : null;
        if (!id) return;
        const index = store.selected.findIndex((item) => item.id === id);
        if (index !== -1) {
            store.selected[index].result = validation;
        }
    });
    store.notifySuccess('USI Verified Successfully');
};

watch(
    () => store.selected,
    (val) => {
        if (val.length === 0) {
            store.formDialog = false;
        }
    }
);
</script>
