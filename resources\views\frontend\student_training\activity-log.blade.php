@extends('frontend.layouts.frontend')
@section('title', $pagetitle )

@section('content')

<!-- Content Wrapper. Contains page content -->
<div class="main-conntent">
    @section('content-header')
    <!-- Content Header (Page header) -->
<!--    <section class="content-header">
        <div class="pull-left">
            <ol class="breadcrumb">
                <li><a href="{{ route('user_dashboard') }}"><i class="fa fa-book"></i> Home</a></li>
                <li><a href="{{ route('search-students') }}"><i class="fa fa-book"></i> Search Student</a></li>
                <li><a href="{{ route('student-profile', array('id' => $studentId)) }}"><i class="fa fa-book"></i> Student Profile</a></li>
                <li><a href="{{ route('student-training-plan', array('student_id' => $studentId, 'id' => '0')) }}"><i class="fa fa-book"></i> Student Training Plan</a></li>
                <li class="active"> {{ Route::current()->getName() == 'edit-traineeship-activity-log' ? 'Edit' : 'Add' }} Activity Log</li>
            </ol>
            <h1> Activity Log for Traineeship </h1>
        </div>
        <div class="pull-right icon-block">
            <ul>
                <li><a href="javascript:;"><img class="img-responsive center-block" src="{{ asset('icon/upload.png') }}"><div class="text-center">Export</div></a></li>
                
            </ul>
        </div>
        <div style="clear: both;"></div>
    </section>-->
    @endsection

    @if ( $errors->count() > 0 )
    <section class="content server-side-validation">
        <div class="row">
            <div class="col-md-12">
                <p>The following errors have occurred:</p>
                <ul class="error-list">
                    @foreach( $errors->all() as $message )
                    <li>{{ $message }}</li>
                    @endforeach
                </ul>
            </div>
        </div>
    </section>
    @endif
    
    <!-- Main content -->
    <section class="content">
        
        <div class="row">
            <div class="col-md-12">
                {{ Form::open( array('method' => 'post', 'files'=>true, 'class' => 'form-horizontal vertical-add-form', 'id' => 'activityInfoForm')) }} 
                <div class="box box-info">
                    <div class="custom-header">
                        <div class="row">
                            <div class="col-md-10">
                                <h3 class="box-title">{{ Route::current()->getName() == 'edit-traineeship-activity-log' ? 'Update' : 'Add' }} Traineeship Activity</h3>
                            </div>
                            <div class="col-md-2">
                                <span class="pull-right add-btn-block">
                                    @if(Route::current()->getName() == 'edit-traineeship-activity-log')
                                    <a href="{{ route('traineeship-activity-log', array('id' => $trainingPlanInfo->id)) }}" data-toggle="tooltip" data-original-title=""><div class="btn-add"><i class="fa fa-reply"></i></div></a></li> 
                                    @else
                                    <a href="{{ route('student-training-plan', array('student_id' => $studentId, 'id' => '0')) }}" data-toggle="tooltip" data-original-title=""><div class="btn-add"><i class="fa fa-reply"></i></div></a></li> 
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group margin-minus-15">
                                    <label class="col-sm-4 control-label"> Student ID : </label>
                                    <div class="col-sm-5 label-value-view" style="color: green !important;">
                                        {{ $studentDetail->generated_stud_id }}
                                        {{ Form::hidden('trainingPlanId', $trainingPlanId, array('id' => 'trainingPlanId')) }}
                                        {{ Form::hidden('student_id', $studentId, array('id' => 'student_id')) }}
                                    </div>
                                </div>
                                <div class="form-group margin-minus-15">
                                    <label class="col-sm-4 control-label"> Name : </label>
                                    <div class="col-sm-5 label-value-view" style="color: green !important;">
                                        {{ $studentDetail->name_title .' '. $studentDetail->first_name .' '. $studentDetail->family_name }}
                                    </div>
                                </div>
                                <div class="form-group margin-minus-15">
                                    <label class="col-sm-4 control-label"> Course : </label>
                                    <div class="col-sm-5 label-value-view" style="color: green !important;">
                                        {{ $trainingPlanInfo->course_code .' : '. $trainingPlanInfo->course_name }}
                                    </div>
                                </div>
                                <div class="form-group margin-minus-15">
                                    <label class="col-sm-4 control-label"> Module : </label>
                                    <div class="col-sm-5 label-value-view" style="color: green !important;">
                                        {{ $trainingPlanInfo->subject_code .' : '. $trainingPlanInfo->subject_name }}
                                    </div>
                                </div>
                                <div class="form-group margin-minus-15">
                                    <label class="col-sm-4 control-label"> Unit : </label>
                                    <div class="col-sm-5 label-value-view" style="color: green !important;">
                                        {{ $trainingPlanInfo->unit_code .' : '. $trainingPlanInfo->unit_name }}
                                    </div>
                                </div>
                                <div class="form-group margin-minus-15">
                                    <label class="col-sm-4 control-label"> Trainer : </label>
                                    <div class="col-sm-5 label-value-view" style="color: green !important;">
                                        {{ $trainingPlanInfo->name_title .' '. $trainingPlanInfo->first_name .' '. $trainingPlanInfo->last_name }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"> Supervisor :<span id="" class="required-field">*<div></div></span></label>
                                    <div class="col-sm-5">
                                        {{ Form::text('supervisor', empty($arrActivityLogInfo->supervisor) ? null : $arrActivityLogInfo->supervisor, array('class' => 'form-control', 'id' => 'supervisor', 'placeholder' => 'Enter Supervisor')) }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"> Visit Date :<span id="" class="required-field">*<div></div></span></label>
                                    <div class="col-sm-3">
                                        {{ Form::text('visit_date', empty($arrActivityLogInfo->visit_date) ? null : date('d-m-Y', strtotime($arrActivityLogInfo->visit_date)), array('class' => 'form-control dateField', 'id' => 'visit_date', 'placeholder' => 'dd-mm-yyyy')) }}
                                    </div>
                                    <div class="col-sm-2">
                                        <div class="input-group">
                                            {{ Form::text('visit_time', empty($arrActivityLogInfo->visit_time) ? null : $arrActivityLogInfo->visit_time, array('class' => 'form-control timepicker', 'id' => 'visit_time')) }}
                                            <!--<div class="input-group-addon">
                                                <i class="fa fa-clock-o"></i>
                                            </div>-->
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"> Type :<span id="" class="required-field">*<div></div></span><span id="" class="required-field">*<div></div></span> </label>
                                    <div class="col-sm-5">
                                        {{ Form::select('type', $arrActivityLogType, empty($arrActivityLogInfo->type) ? null : $arrActivityLogInfo->type, array('class' => 'form-control', 'id' => 'type')) }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"> Completed ? </label>
                                    <div class="col-sm-5 label-value-view">
                                        {{ Form::radio('is_completed', '1', (Route::current()->getName() == 'traineeship-activity-log') ? 'true' : (isset($arrActivityLogInfo->is_completed) && $arrActivityLogInfo->is_completed == 1 ? true : false) , array('class' => '', 'id' => 'yes')) }} <label for="yes">Yes (Visited)</label>
                                        &nbsp;&nbsp;&nbsp;
                                        {{ Form::radio('is_completed', '0', isset($arrActivityLogInfo->is_completed) && $arrActivityLogInfo->is_completed == 0 ? true : false , array('class' => '', 'id' => 'no')) }} <label for="no">No (Scheduled)</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-4 control-label"> Notes :<span id="" class="required-field">*<div></div></span> </label>
                                    <div class="col-sm-5">
                                        {{ Form::textarea('note', empty($arrActivityLogInfo->note) ? null : $arrActivityLogInfo->note, array('class' => 'form-control', 'id' => 'note', 'placeholder' => 'Enter Notes')) }}
                                    </div>
                                </div>
                                <div class="form-group ">
                                    <label class="col-sm-4 control-label"> File : </label>
                                    <div class="col-sm-5">
                                        <!--{{ Form::file('attach_file', null, empty($arrActivityLogInfo->file_name) ? null : $arrActivityLogInfo->file_name, array('class' => 'form-control', 'id' => 'attach_file')) }}-->
                                        {{ Form::file('attach_file', ['class' => 'inputfile','id'=>'attach_file']) }}
                                        <label for="attach_file"><i class="fa fa-upload"></i><span class='file-name'> Choose a File&hellip;</span></label>
                                    </div>
                                </div>
                                <div class="form-group margin-minus-15">
                                    <label class="col-sm-4 control-label"> &nbsp; &nbsp; </label>
                                    <div class="col-sm-5">
                                        <button type="submit" class="btn disable btn-info"> 
                                            {{ Route::current()->getName() == 'edit-traineeship-activity-log' ? 'Update' : 'Add' }}
                                        </button>
                                        @if(Route::current()->getName() == 'edit-traineeship-activity-log')
                                            <a href="{{ route('edit-traineeship-activity-log', array('training_plan_id' => $trainingPlanInfo->id, 'id' => $arrActivityLogInfo->id)) }}" class="btn disable btn-info"> Cancel </a>
                                        @else
                                            <a href="{{ route('traineeship-activity-log', array('id' => $trainingPlanInfo->id)) }}" class="btn disable btn-info"> Cancel </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{ Form::close() }}
            </div>
        </div>
        
        @if(Route::current()->getName() == 'traineeship-activity-log')
        <div class="row" id="existedTrainingPlanModuleSection">
            <div class="col-md-12">
                    <div class="custom-header">
                        <h3 class="box-title"> Traineeship Activity Log List </h3>
                    </div>
                    <div class="box-body table-responsive no-padding">
                        <table id="activityLogData" class="table table-hover table-custom">
                            <thead>
                                <tr>
                                    <th> Module </th>
                                    <th> Unit </th>
                                    <th> Trainer </th>
                                    <th> Supervisor </th>
                                    <th> Visit Date & Time </th>
                                    <th> Type </th>
                                    <th> Duration (Minutes) </th>
                                    <th> Completed </th>
                                    <th> Notes </th>
                                    <!--<th> Attached File </th>-->
                                </tr>
                            </thead>
                            <tbody>
                                @if($arrActivityLogList->count() > 0)
                                @foreach($arrActivityLogList as $row)

                                <tr>
                                    <td> {{ $row->subject_code }}
                                        <div class="action-overlay">
                                            <ul class="icon-actions-set">
                                                <li>
                                                    {{-- <a href="{{ route('edit-traineeship-activity-log', array('id' => $trainingPlanInfo->id, 'id' => $row->id)) }}" class="link-black text-sm" data-toggle="tooltip" data-original-title="Edit"> <i class="fa fa-edit"></i> </a> --}}
                                                </li>
                                                <li>
                                                    <span data-toggle="modal" class="delete" data-id="{{ $row->id }}" data-target="#deleteModal"> 
                                                        <a  class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i> </a> 
                                                    </span>
                                                </li>
                                                <li>
                                                    @if($row->file_name != '')
                                                        @php $primaryId = base64_encode($row->id); @endphp
                                                        <a href="javascript:;" class="downloadTraineeshipFile" data-toggle="tooltip" data-original-title="Download File" data-id="{{ $primaryId }}"> <i class="fa fa-download"></i></a>
                                                    @else
                                                        <a href="javascript:;" class="" data-toggle="tooltip" data-original-title="Not Attached File"> <i class="fa fa-warning"></i></a>
                                                    @endif
                                                </li>
                                            </ul>
                                        </div>  
                                    </td>
                                    <td> {{ $row->unit_code }} </td>
                                    <td> {{ $row->name_title .' '. $row->first_name .' '. $row->last_name }} </td>
                                    <td> {{ $row->supervisor }} </td>
                                    <td> {{ date('d/m/Y', strtotime($row->visit_date)) .' '. date('h:i:s A', strtotime($row->visit_time)) }} </td>
                                    <td> {{ $arrActivityLogType[$row->type] }} </td>
                                    <td> 0 </td>
                                    <td> {{ Form::checkbox('is_completed', '1', (isset($row->is_completed) && $row->is_completed == '1') ? 'true' : false, array('disabled')) }} </td>
                                    <td> {{ $row->note }} </td>
                                </tr>
                                @endforeach
                                @endif
                                @if($arrActivityLogList->count() == 0)
                                <tr>
                                    <td colspan="9" style="text-align: center">
                                        <p style="color:red;">No Record Found</p>
                                    </td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                    <div class="box-footer clearfix text-center">
                        <ul class="pagination pagination-sm no-margin">
                            {{ $arrActivityLogList->links() }}
                        </ul>
                    </div>
            </div>
        </div>
        
        <div class="modal fade" id="deleteModal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Delete Record</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <p> You want to delete record. Are you sure?</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success yes-sure" type="button" data-id="">Yes</button>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </section>
</div>
<style>
    .error {
        border: 1px solid red !important;
    }
</style>
<!-- /.content-wrapper -->
@endsection
