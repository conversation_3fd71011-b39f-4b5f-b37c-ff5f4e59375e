<script setup>
import USIStudentListComponent from '@spa/modules/usi-verifications/USIStudentListComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Student USI Verification" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Student USI Verification" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <USIStudentListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
