<template>
    <Dialog
        v-if="visible"
        title="Edit Import Record"
        :width="800"
        @close="closeModal"
        :class="'k-modal-window'"
        :dialog-class="'tw-dialog custom-modal-wrapper'"
        append-to="body"
    >
        <div v-if="importData" class="space-y-4 p-4">
            <form @submit.prevent="saveChanges" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <!-- Student ID -->
                    <div>
                        <label for="studentId" class="block text-sm font-medium text-gray-700"
                            >Student ID</label
                        >
                        <input
                            id="studentId"
                            v-model="formData.StudentId"
                            type="text"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            required
                        />
                        <p v-if="errors.StudentId" class="mt-1 text-sm text-red-600">
                            {{ errors.StudentId }}
                        </p>
                    </div>

                    <!-- Unit ID -->
                    <div>
                        <label for="unitId" class="block text-sm font-medium text-gray-700"
                            >Unit ID</label
                        >
                        <input
                            id="unitId"
                            v-model="formData.UnitId"
                            type="text"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            required
                        />
                        <p v-if="errors.UnitId" class="mt-1 text-sm text-red-600">
                            {{ errors.UnitId }}
                        </p>
                    </div>

                    <!-- Course Type -->
                    <div>
                        <label for="courseType" class="block text-sm font-medium text-gray-700"
                            >Course Type</label
                        >
                        <input
                            id="courseType"
                            v-model="formData.CourseType"
                            type="text"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            required
                        />
                        <p v-if="errors.CourseType" class="mt-1 text-sm text-red-600">
                            {{ errors.CourseType }}
                        </p>
                    </div>

                    <!-- Total Mark -->
                    <div v-if="formData.CourseType === 'HigherEd'">
                        <label for="total" class="block text-sm font-medium text-gray-700"
                            >Total Mark</label
                        >
                        <input
                            id="total"
                            v-model="formData.TotalMark"
                            type="number"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p v-if="errors.TotalMark" class="mt-1 text-sm text-red-600">
                            {{ errors.TotalMark }}
                        </p>
                    </div>

                    <!-- Final Outcome -->
                    <div v-if="formData.CourseType === 'VET'">
                        <label for="finalOutcome" class="block text-sm font-medium text-gray-700"
                            >Final Outcome</label
                        >
                        <input
                            id="finalOutcome"
                            v-model="formData.FinalOutcome"
                            type="text"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                        />
                        <p v-if="errors.FinalOutcome" class="mt-1 text-sm text-red-600">
                            {{ errors.FinalOutcome }}
                        </p>
                    </div>
                </div>

                <!-- Assessment Items -->
                <div v-if="hasAssessmentItems">
                    <h3 class="mb-2 text-sm font-semibold text-gray-700">Assessment Items</h3>
                    <div class="space-y-2 rounded-md bg-gray-50 p-4">
                        <div
                            v-for="(value, key) in formData.assessmentData"
                            :key="key"
                            class="flex items-center space-x-2"
                        >
                            <label
                                :for="`assessment-${key}`"
                                class="block w-1/3 text-sm font-medium text-gray-700"
                                >{{ key }}</label
                            >
                            <input
                                :id="`assessment-${key}`"
                                v-model="formData.assessmentData[key]"
                                type="text"
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            />
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <DialogActionsBar>
            <div class="flex justify-end space-x-2">
                <Button variant="secondary" @click="closeModal"> Cancel </Button>
                <Button variant="primary" @click="saveChanges" :disabled="saving">
                    {{ saving ? 'Saving...' : 'Save Changes' }}
                </Button>
            </div>
        </DialogActionsBar>
    </Dialog>
</template>

<script>
import { Dialog, DialogActionsBar } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import { reactive, computed, ref, watch } from 'vue';

export default {
    components: {
        Dialog,
        DialogActionsBar,
        Button,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        importData: {
            type: Object,
            default: null,
        },
        saving: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        const formData = reactive({
            StudentId: '',
            UnitId: '',
            CourseType: '',
            TotalMark: '',
            FinalOutcome: '',
            assessmentData: {},
        });

        const errors = ref({});

        // Watch for changes in importData and update formData
        watch(
            () => props.importData,
            (newVal) => {
                if (newVal) {
                    formData.StudentId = newVal.import_data?.StudentId || '';
                    formData.UnitId = newVal.import_data?.UnitId || '';
                    formData.CourseType = newVal.import_data?.CourseType || '';
                    formData.TotalMark = newVal.import_data?.TotalMark || '';
                    formData.FinalOutcome = newVal.import_data?.FinalOutcome || '';
                    formData.assessmentData = { ...(newVal.import_data?.assessmentData || {}) };
                }
            },
            { immediate: true, deep: true }
        );

        const hasAssessmentItems = computed(() => {
            return Object.keys(formData.assessmentData || {}).length > 0;
        });

        const validateForm = () => {
            const newErrors = {};

            if (!formData.StudentId) {
                newErrors.StudentId = 'Student ID is required';
            }

            if (!formData.UnitId) {
                newErrors.UnitId = 'Unit ID is required';
            }

            if (!formData.CourseType) {
                newErrors.CourseType = 'Course Type is required';
            }

            errors.value = newErrors;
            return Object.keys(newErrors).length === 0;
        };

        const closeModal = () => {
            emit('update:visible', false);
            errors.value = {};
        };

        const saveChanges = () => {
            if (validateForm()) {
                emit('save', {
                    id: props.importData.id,
                    import_data: {
                        ...formData,
                    },
                });
            }
        };

        return {
            formData,
            errors,
            hasAssessmentItems,
            closeModal,
            saveChanges,
        };
    },
};
</script>
