<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Support\Traits\CreaterUpdaterTrait;

class ResultImport extends Model
{
    use CreaterUpdaterTrait;

    protected $table = 'result_imports';

    protected $fillable = [
        'import_data',
        'status',
        'error_message',
        'import_batch_id',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'import_data' => 'array',
    ];
}
