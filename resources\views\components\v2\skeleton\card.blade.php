@props([
'section' => 'progress',
'height' => 'auto'
])
<div {{$attributes->twMerge('py-4 px-6 bg-white shadow rounded-md w-full')->merge(['style' => "height: {$height};"])}}>
  <!-- Header -->
  <div class="flex gap-4 items-center justify-between">
    <div class="w-32 h-7 bg-gray-200 rounded animate-pulse"></div>
    <div class="w-20 h-7 bg-gray-200 rounded animate-pulse"></div>
  </div>

  <!-- Section Content -->
  @if ($section === 'progress')
  <div class="flex items-center gap-12 mt-5">
    @foreach (range(1, 2) as $item)
    <div class="flex gap-2 items-center">
      <div class="w-[56px] h-[56px] bg-gray-200 rounded-full animate-pulse"></div>
      <div class="col-span-2 flex flex-col gap-1 justify-center">
        <div class="w-[100px] h-5 bg-gray-200 rounded animate-pulse"></div>
        <div class="w-[80px] h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
    @endforeach
  </div>
  <x-v2.skeleton.timeline />
  @elseif ($section === 'course')
  @foreach (range(1, 3) as $row)
  <div class="flex gap-1 w-full border-b pb-2 mt-4">
    @foreach (range(1, 6) as $col)
    <div class="inline-flex flex-col space-y-1 justify-center w-1/6 h-full">
      <div class="w-3/4 h-3 bg-gray-200 rounded animate-pulse mb-2"></div>
      <div class="w-full h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
    </div>
    @endforeach
  </div>
  @endforeach
  @elseif ($section === 'attendance')
  <div class="w-full mt-5">
    <div class="w-full h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
    <div class="w-3/4 h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
  </div>
  @else
  <div class="w-full mt-5">
    <div class="w-full h-5 bg-gray-200 rounded animate-pulse mb-2"></div>
    <div class="w-3/4 h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
  </div>
  @endif
</div>