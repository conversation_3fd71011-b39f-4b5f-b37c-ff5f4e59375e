<?php

namespace App\Services\ResultImport;

use App\Model\v2\AssessmentTask;
use App\Model\v2\AssignedAssessmentTask;
use App\Model\v2\CourseType;
use App\Model\v2\ResultGrade;
use App\Model\v2\Student;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\UnitModule;
use Illuminate\Support\Facades\Log;

class ResultImportValidator
{
    /**
     * Validate the import record
     *
     * @param  \App\Model\v2\ResultImport  $importRecord
     * @return array [$errors, $related]
     */
    public function validate($importRecord)
    {
        $errors = [];
        $related = [];
        $importData = $importRecord->import_data;

        Log::info('Import Data: '.json_encode($importData));

        // Check if import data is empty
        if (empty($importData)) {
            $errors[] = $this->createError('data', 'Missing Data', 'Import data is empty or invalid.');

            return [json_encode($errors), $related];
        }

        // Validate required fields
        $this->validateStudentId($importData, $errors, $related);
        $this->validateUnitId($importData, $errors, $related);
        $this->validateCourseType($importData, $errors);
        // $this->validateGrade($importData, $errors, $related);
        $this->validateTotal($importData, $errors);
        if (isset($related['student']) && isset($related['unit'])) {
            $this->validateEnrollment($importData, $errors, $related);
        }
        if (isset($related['student']) && isset($related['unit']) && isset($related['enrollment'])) {
            $this->validateAssessmentData($importData, $errors, $related);
        }

        // Validate student enrollment if student and subject are valid

        return [json_encode($errors), $related];
    }

    /**
     * Create an error array with consistent structure
     *
     * @param  string  $type
     * @param  string  $title
     * @param  string  $description
     * @return array
     */
    private function createError($type, $title, $description)
    {
        return [
            'error_type' => $type,
            'error_title' => $title,
            'error_description' => $description,
        ];
    }

    /**
     * Validate student ID
     *
     * @param  array  $importData
     * @param  array  &$errors
     * @param  array  &$related
     */
    private function validateStudentId($importData, &$errors, &$related)
    {
        if (empty($importData['StudentId'])) {
            $errors[] = $this->createError('StudentId', 'Missing Student ID', 'Student ID is required.');

            return;
        }

        $student = Student::where('generated_stud_id', trim($importData['StudentId']))->first();
        if (! $student) {
            $errors[] = $this->createError(
                'StudentId',
                'Invalid Student ID',
                'Student with ID '.$importData['StudentId'].' not found.'
            );
        } else {
            $related['student'] = $student;
        }
    }

    /**
     * Validate unit ID
     *
     * @param  array  $importData
     * @param  array  &$errors
     * @param  array  &$related
     */
    private function validateUnitId($importData, &$errors, &$related)
    {
        if (empty($importData['UnitId'])) {
            $errors[] = $this->createError('UnitId', 'Missing Unit ID', 'Unit ID is required.');

            return;
        }

        $subject = UnitModule::where('unit_code', trim($importData['UnitId']))->first();
        if (! $subject) {
            $errors[] = $this->createError(
                'UnitId',
                'Invalid Unit ID',
                'Unit with ID '.$importData['UnitId'].' not found.'
            );
        } else {
            $related['unit'] = $subject;
        }
    }

    /**
     * Validate course type
     *
     * @param  array  $importData
     * @param  array  &$errors
     */
    private function validateCourseType($importData, &$errors)
    {
        $courseType = CourseType::query()->where('title', $importData['CourseType'])->first();
        if (empty($importData['CourseType'])) {
            $errors[] = $this->createError('CourseType', 'Missing Course Type', 'Course Type is required.');
        } elseif (in_array($importData['CourseType'], ['HigherEd', 'VET']) === false) {
            $errors[] = $this->createError('CourseType', 'Invalid Course Type', 'Course Type must be HigherEd or VET.');
        } elseif (! $courseType) {
            $errors[] = $this->createError('CourseType', 'Invalid Course Type', 'Course Type '.$importData['CourseType'].' not found.');
        }
    }

    /**
     * Validate grade
     *
     * @param  array  $importData
     * @param  array  &$errors
     * @param  array  &$related
     */
    private function validateGrade($importData, &$errors, &$related)
    {
        if (empty($importData['Grade'])) {
            $errors[] = $this->createError('Grade', 'Missing Grade', 'Grade is required.');

            return;
        }

        $grade = ResultGrade::where('grade', trim($importData['Grade']))->first();
        if (! $grade) {
            $errors[] = $this->createError(
                'Grade',
                'Invalid Grade',
                'Grade '.$importData['Grade'].' not found in the system.'
            );
        } else {
            $related['grade'] = $grade;
        }
    }

    /**
     * Validate total mark
     *
     * @param  array  $importData
     * @param  array  &$errors
     */
    private function validateTotal($importData, &$errors)
    {
        // Validate Total field
        if ($importData['CourseType'] == 'HigherEd') {
            if (empty($importData['TotalMark'])) {
                $errors[] = $this->createError('Total', 'Missing Total', 'Total mark is required.');
            } elseif (! is_numeric($importData['TotalMark'])) {
                $errors[] = $this->createError('Total', 'Invalid Total', 'Total mark must be a numeric value.');
            } elseif ((float)$importData['TotalMark'] < 0 || (float)$importData['TotalMark'] > 100) {
                $errors[] = $this->createError('Total', 'Invalid Total', 'Total mark must be between 0 and 100.');
            } elseif ($importData['TotalMark'] == '' && $importData['CourseType'] == 'HigherEd') {
                $errors[] = $this->createError('Total', 'Invalid Total', 'Total mark can not be empty for Higher Education courses.');
            }

            // Validate TotalMark field (if using different field name)
            if (isset($importData['TotalMark']) && ! is_numeric($importData['TotalMark'])) {
                $errors[] = $this->createError('TotalMark', 'Invalid Total Mark', 'Total Mark must be a numeric value.');
            }
        } elseif ($importData['CourseType'] == 'VET') {
            if (empty($importData['FinalOutcome'])) {
                $errors[] = $this->createError('FinalOutcome', 'Missing FinalOutcome', 'FinalOutcome is required.');
            } elseif (! in_array($importData['FinalOutcome'], ['C', 'NYC'])) {
                $errors[] = $this->createError('FinalOutcome', 'Invalid FinalOutcome', 'FinalOutcome must be C or NYC.');
            }
        }

    }

    /**
     * Validate assessment data
     *
     * @param  array  $importData
     * @param  array  &$errors
     * @param  array  &$related
     */
    private function validateAssessmentData($importData, &$errors, &$related)
    {
        // Ensure student and subject are set before proceeding
        if (! isset($related['student']) || ! isset($related['unit']) || ! isset($related['enrollment'])) {
            return;
        }

        $findStudentId = $related['student'];
        $findUnitId = $related['unit'];
        $findStudentSubjectEnrollmentInfo = StudentSubjectEnrolment::where([
            'student_id' => $findStudentId->id,
            'unit_id' => $findUnitId->id,
        ])->first();

        if (isset($importData['assessmentData']) && ! is_array($importData['assessmentData']) && ! is_numeric($importData['assessmentData'])) {
            $errors[] = $this->createError(
                'assessment',
                'Invalid Assessment Items',
                'Assessment items must be an array.'
            );
        } else {
            foreach ($importData['assessmentData'] as $key => $value) {
                if ($importData['CourseType'] == 'VET') {
                    if (! in_array($value, ['S', 'NYS'])) {
                        $errors[] = $this->createError(
                            'assessment',
                            'Invalid Assessment Items',
                            'Assessment items must be S or NYS for task '.$key.'.'
                        );
                    }
                } elseif ($importData['CourseType'] == 'HigherEd') {
                    if (! is_numeric($value)) {
                        $errors[] = $this->createError(
                            'assessment',
                            'Invalid Assessment Items',
                            'Assessment items must be a numeric value for task '.$key.'.'
                        );
                    }
                    if ((float)$value < 0 || (float)$value > 100) {
                        $errors[] = $this->createError(
                            'assessment',
                            'Invalid Assessment Items',
                            'Assessment items must be between 0 and 100 for task '.$key.'.'
                        );
                    }
                }

                $assessmentTaskInfo = AssessmentTask::where('task_name', trim($key))->where('subject_id', $findUnitId->id)->first();

                if ($assessmentTaskInfo) {
                    $assignedAssessmentTaskInfo = AssignedAssessmentTask::where([
                        'assessment_task_id' => $assessmentTaskInfo->id,
                        'batch' => trim($findStudentSubjectEnrollmentInfo->batch),
                    ])->first();

                    if (! $assignedAssessmentTaskInfo) {
                        $errors[] = $this->createError(
                            'assessment',
                            'Assessment Items Not Assigned to Batch',
                            $key.' task is not assigned to batch '.$findStudentSubjectEnrollmentInfo->batch.' for student '.$importData['StudentId']
                        );
                    }
                } else {
                    $errors[] = $this->createError(
                        'assessment',
                        'Assessment Items Not Found',
                        $key.' task is Not Found.'
                    );
                }
            }
        }
    }

    /**
     * Validate student enrollment
     *
     * @param  array  $importData
     * @param  array  &$errors
     * @param  array  &$related
     */
    private function validateEnrollment($importData, &$errors, &$related)
    {
        $enrollment = StudentSubjectEnrolment::where([
            'student_id' => $related['student']->id,
            'unit_id' => $related['unit']->id,
        ])->first();

        if (! $enrollment) {
            $errors[] = $this->createError(
                'enrollment',
                'Enrollment Not Found',
                'Student '.$importData['StudentId'].' is not enrolled in unit '.$importData['UnitId'].'.'
            );
        } else {
            $related['enrollment'] = $enrollment;
        }
    }
}
