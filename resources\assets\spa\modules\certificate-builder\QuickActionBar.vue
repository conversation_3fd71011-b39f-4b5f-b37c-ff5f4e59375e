<template>
    <v-group
        ref="transformerNode"
        v-if="selectedNode && !isDragging"
        :config="{ x: toolbarPos.x, y: toolbarPos.y, type: 'QUICKACTION' }"
    >
        <v-rect
            :config="{
                width: isQR ? 80 : 114,
                height: 32,
                fill: 'white',
                cornerRadius: 50,
                shadowBlur: 1,
                type: 'QUICKACTION',
            }"
        />
        <v-image
            @click="handleDelete"
            :config="{
                image: deleteIcon,
                x: 12,
                y: 6,
                width: 20,
                height: 20,
                cursor: 'pointer',
                type: 'QUICKACTION',
            }"
        />
        <v-image
            @click="handleLock"
            :config="{
                image: isLocked ? lockIcon : unlockIcon,
                x: 44,
                y: 6,
                width: 20,
                height: 20,
                cursor: 'pointer',
                type: 'QUICKACTION',
            }"
        />
        <v-image
            @click="handleDuplicate"
            v-if="!isQR"
            :config="{
                image: duplicateIcon,
                x: 78,
                y: 6,
                width: 20,
                height: 20,
                cursor: 'pointer',
                type: 'QUICKACTION',
            }"
        />
    </v-group>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, computed } from 'vue';
const props = defineProps(['selectedNode']);
const emit = defineEmits(['deleteNode', 'duplicateNode', 'lockNode']);
const transformerNode = ref(null);

// Icons
const deleteIcon = ref(null);
const lockIcon = ref(null);
const unlockIcon = ref(null);
const duplicateIcon = ref(null);
const isLocked = ref(false);
const isDragging = ref(false);
const toolbarPos = ref({ x: 0, y: 0 });

const loadImage = (src) => {
    return new Promise((resolve) => {
        const img = new Image();
        img.src = src;
        img.onload = () => resolve(img);
    });
};

const isQR = computed(() => {
    if (!props.selectedNode) return false;
    const node = props.selectedNode.getNode();
    return node.attrs.type === 'QR';
});

onMounted(async () => {
    deleteIcon.value = await loadImage('/v2/icons/delete.svg'); // Local path
    lockIcon.value = await loadImage('/v2/icons/lock.svg');
    unlockIcon.value = await loadImage('/v2/icons/unlock.svg');
    duplicateIcon.value = await loadImage('/v2/icons/duplicate.svg');
});

onMounted(() => {});

const handleDelete = () => {
    emit('deleteNode', props.selectedNode);
};

const handleLock = () => {
    isLocked.value = !isLocked.value;
    props.selectedNode.getNode().draggable(!isLocked.value);
};

const handleDuplicate = () => {
    if (!props.selectedNode) return;

    const originalNode = props.selectedNode.getNode();
    console.log('orginal', originalNode);
    const newPosition = {
        x: originalNode.x() + 30,
        y: originalNode.y() + 50,
    };

    const clonedProps = {
        ...originalNode.attrs,
        x: newPosition.x,
        y: newPosition.y,
        id: `duplicate-${Date.now()}`,
    };

    emit('duplicateNode', clonedProps);
};

const updateToolbarPosition = (node) => {
    const position = node.absolutePosition();
    toolbarPos.value = {
        x: position.x,
        y: position.y - 44,
    };
};

const detachToolbar = () => {
    isDragging.value = true;
};

const attachToolbar = (node) => {
    if (!node) return;
    const selectedNode = node.getNode();
    isDragging.value = false;
    nextTick(() => {
        updateToolbarPosition(selectedNode);
    });
};

watch(
    () => props.selectedNode,
    async (newNode) => {
        await nextTick();
        if (newNode) {
            const node = newNode.getNode();
            isLocked.value = !node.attrs.draggable;

            updateToolbarPosition(node);

            // Add drag event listeners
            node.on('dragstart', () => detachToolbar());
            node.on('dragend', () => attachToolbar(newNode));
            node.on('transform', () => detachToolbar());
            node.on('transformend', () => attachToolbar(newNode));
        }
    }
);

// watch(
//     () => transformerNode.value,
//     (newNode) => {
//         if (newNode) {
//             const transformer = newNode?.getNode();
//             console.log("newNode", transformer)
//             if (transformer) {
//                 transformer?.nodes([]);
//                 transformer?.getLayer()?.batchDraw();
//             }
//         }
//     }
// );

defineExpose({
    detachToolbar,
    attachToolbar,
});
</script>
