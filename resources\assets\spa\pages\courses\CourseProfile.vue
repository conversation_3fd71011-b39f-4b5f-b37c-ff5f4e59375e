<template>
    <Layout :noSpacing="true" :loading="true">
        <Head title="Add New Course" />
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="getPageTitle" :linkurl="getCourseListUrl" :back="true" />
        </template>
        <CourseProfileMenu
            :currentstep="currentposition"
            :mode="'profile'"
            :steps="getConfig"
            @change="changeStep"
        />
        <div class="px-8 py-6">
            <div v-for="(tab, index) in getConfig.order" :key="index">
                <div
                    class="container mx-auto"
                    v-if="tab.name == 'general' && index == currentposition"
                >
                    <GeneralForm @saved="handleSave" />
                </div>
                <div
                    class="container mx-auto mb-24 w-full rounded-md bg-white p-6 shadow-sm"
                    v-if="tab.name == 'fees' && index == currentposition"
                >
                    <HoursFees @saved="handleSave" />
                </div>
                <div
                    class="container mx-auto mb-24 w-full rounded-md bg-white p-6 shadow-sm"
                    v-if="tab.name == 'enrollments' && index == currentposition"
                >
                    <EnrollmentsGrid @saved="handleSave" />
                </div>
                <div
                    class="container mx-auto mb-24 w-full rounded-md bg-white p-6 shadow-sm"
                    v-if="tab.name == 'faculties' && index == currentposition"
                >
                    <FacultiesCampus @saved="handleSave" />
                </div>
                <div
                    class="container mx-auto mb-24 w-full rounded-md bg-bluegray-50 shadow-md"
                    v-if="tab.name == 'units' && index == currentposition"
                >
                    <!-- <UnitsBasic
                    @saved="handleSave"
                    v-if="unitSetupMode == 'basic'"
                />
                <Units @saved="handleSave" v-else /> -->
                    <manageTemplates :setupMode="unitSetupMode" />
                </div>
                <div
                    class="container mx-auto mb-24 w-full rounded-md bg-white p-6 shadow-sm"
                    v-if="tab.name == 'intakes' && index == currentposition"
                >
                    <ShortCourseIntakes />
                </div>
                <div
                    class="container mx-auto mb-24 w-full rounded-md bg-white p-6 shadow-sm"
                    v-if="tab.name == 'avetmiss' && index == currentposition"
                >
                    <Avetmiss @saved="handleSave" />
                </div>
                <div
                    class="container mx-auto mb-24 w-full rounded-md bg-white p-6 shadow-sm"
                    v-if="tab.name == 'info' && index == currentposition"
                >
                    <CourseHigherEdInfo @saved="handleSave" />
                </div>
            </div>
        </div>
    </Layout>
</template>
<script setup>
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import Layout from '../Layouts/Layout';
import PageTitleContent from '../Layouts/PageTitleContent';
import { router, Head, Link } from '@inertiajs/vue3';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import CourseProfileMenu from '@spa/pages/courses/includes/CourseProfileMenu.vue';
import GeneralForm from '@spa/pages/courses/includes/GeneralForm.vue';
import HoursFees from '@spa/pages/courses/includes/HoursFees.vue';
import FacultiesCampus from '@spa/pages/courses/includes/FacultiesCampus.vue';
import Avetmiss from '@spa/pages/courses/includes/Avetmiss.vue';
import EnrollmentsGrid from '@spa/pages/courses/includes/Enrollments.vue';
import CourseHigherEdInfo from '@spa/pages/courses/includes/CourseHigherEdInfo.vue';
import { useCoursesStore } from '@spa/stores/modules/courses';
import globalHelper from '@spa/plugins/global-helper';
import ShortCourseIntakes from '@spa/pages/courses/includes/ShortCourseIntakes.vue';
import manageTemplates from '@spa/pages/courses/includes/templates/entry.vue';

const props = defineProps({
    user: Object,
    course: Object,
    units: Object,
    enrollments: Object,
    templates: Object,
    formSeeds: Object,
    years: Object,
    course_structure: Object,
});

const store = useCoursesStore();
const { currentposition } = storeToRefs(store);
const getCourseListUrl = computed(() => {
    return route('spa.courses.index', store.courseListFilters);
});
store.updateConfigRoot({
    tabs: 7,
    order: [
        { name: 'general', show: true },
        { name: 'fees', show: true },
        { name: 'faculties', show: true },
        { name: 'units', show: true },
        { name: 'intakes', show: true },
        { name: 'avetmiss', show: true },
        { name: 'info', show: true },
        /*{ name: 'enrollments', show: false },*/
    ],
});
store.setOperationMode('profile');
store.setDefaultStep();
store.setCourse(props.course);
store.setInits(props.formSeeds);
store.setUnitVariables(props.units);
store.setEnrollments(props.enrollments);
store.setTemplates(props.templates);
store.pickDefaultTemplate();
store.setYearsFilters(props.years);
store.setCourseStructure(props.course_structure?.data);

const getPageTitle = computed(() => {
    return (
        props.course?.general?.data.course_code + ' - ' + props.course?.general?.data.course_name
    );
});
const changeStep = (newstep) => {
    const newStep = newstep < 0 || newstep > getConfig.tabs ? currentposition : newstep;
    store.setCurrentStep(newStep);
};
const getConfig = computed(() => {
    const activeTabs = store.config.order.filter((item) => item.show === true);
    return { tabs: activeTabs.length, order: activeTabs };
});
const handleSave = (resp) => {
    if (resp.success) globalHelper.methods.showPopupSuccess(resp.message, 'Success');
    else globalHelper.methods.showPopupError(resp.message, 'Error');
    return false;
};
const unitSetupMode = computed(() => {
    if (store.course.course_type_id !== 2 && store.course.course_type_id !== 17) return 'basic';
    return 'templates';
});
</script>
