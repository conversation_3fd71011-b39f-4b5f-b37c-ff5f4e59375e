<?php

namespace App\DTO\Courses;

use Support\Traits\ArrayToProps;

class CourseCustomUnitDTO
{
    use ArrayToProps;

    public $id;

    public $college_id;

    public $course_id;

    public $course_subject_id;

    public $unit_type;

    public $tution_fees;

    public $domestic_tution_fees;

    public $delivery_mode;

    public $internal;

    public $external;

    public $workplace_based_delivery;

    public $nominal_hours;

    public $contact_hours;

    public $AVETMISS_Report;

    public $field_education;

    public $unit_code;

    public $vet_unit_code;

    public $unit_name;

    public $description;

    public $module_unit_flag;

    public $vet_flag;

    public $work_placement;

    public $status;

    public $subject_code;

    public $subject_name;

    public $subject_type;

    public $level;

    public $is_long_indicator;

    public $is_assessment;

    public $inc_in_certificate;

    public $is_active;

    public $subject_fee;

    public $domestic_subject_fee;

    public $course_stage;

    public $credit_point;

    public $discipline_broad_type;

    public $discipline_narrow_sub_type;

    public $discipline_narrow_type;

    public $grading_type;

    public $max_marks_allow;

    public $EFTSL_study_load;
}
