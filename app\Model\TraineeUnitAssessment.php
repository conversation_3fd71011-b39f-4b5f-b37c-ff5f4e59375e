<?php

namespace App\Model;

use Auth;
use Illuminate\Database\Eloquent\Model;

class TraineeUnitAssessment extends Model
{
    protected $table = 'rto_traineeship_unit_assessment';

    public function saveUnitAssessmentInfo($collegeId, $request)
    {

        $userId = Auth::user()->id;
        $objUnitAssessment = new TraineeUnitAssessment;

        $objUnitAssessment->college_id = $collegeId;
        $objUnitAssessment->student_id = $request->input('student_id');
        $objUnitAssessment->training_plan_id = $request->input('training_plan_id');
        $objUnitAssessment->task_name = ($request->input('task_name') != '') ? $request->input('task_name') : null;
        $objUnitAssessment->unit_id = ($request->input('unit') != '') ? $request->input('unit') : null;
        $objUnitAssessment->description = ($request->input('description') != '') ? $request->input('description') : null;
        $objUnitAssessment->due_date = ($request->input('due_date') != '') ? date('Y-m-d', strtotime($request->input('due_date'))) : null;
        $objUnitAssessment->due_time = ($request->input('due_time')) ? $request->input('due_time') : null;
        $objUnitAssessment->is_competency = ($request->input('is_competency') != '') ? $request->input('is_competency') : null;

        $objUnitAssessment->created_by = $userId;
        $objUnitAssessment->updated_by = $userId;

        $objUnitAssessment->save();
    }

    public function updateUnitAssessmentInfo($collegeId, $dataArr, $userId)
    {

        $result = TraineeUnitAssessment::where('id', '=', $dataArr['id'])
            ->where('college_id', '=', $collegeId)
            ->update(['competency' => $dataArr['competency'],
                'comment' => $dataArr['comment'],
                'is_mark' => ($dataArr['competency'] != '') ? '1' : '0',
                'updated_by' => $userId]);

        if ($result) {
            return ['status' => 'true', 'action' => 'alert-success', 'msg' => 'Traineeship Unit Assessment Info Update Successfully.'];
        } else {
            return ['status' => 'false', 'action' => 'alert-danger', 'msg' => 'Error occured while Update Traineeship Unit Assessment Info. Please try again.'];
        }
    }

    public function updateLockUnlockFinaliseInfo($collegeId, $dataArr, $userId)
    {
        // print_r($dataArr['id']);exit;
        if ($dataArr['status'] == '1') {
            $status = 'Lock';
            $field = 'is_lock';
        } elseif ($dataArr['status'] == '0') {
            $status = 'Unlock';
            $field = 'is_lock';
        } elseif ($dataArr['status'] == 'finalise') {
            $status = 'Finalise';
            $field = 'is_finalise';
            $dataArr['status'] = '1';
        }

        $result = TraineeUnitAssessment::whereIn('id', $dataArr['id'])
            ->where('college_id', '=', $collegeId)
            ->update([$field => $dataArr['status'], 'updated_by' => $userId]);

        if ($result) {
            return ['status' => 'true', 'action' => 'alert-success', 'msg' => 'Selected Task '.$status.' Successfully.'];
        } else {
            return ['status' => 'false', 'action' => 'alert-danger', 'msg' => 'Error occured while'.$status.'Traineeship Unit Assessment Info. Please try again.'];
        }
    }

    public function getUnitAssessmentList($collegeId, $studentId, $subjectUnitId)
    {

        $unitAssessmentList = TraineeUnitAssessment::from('rto_traineeship_unit_assessment as rtua')
            ->leftjoin('rto_subject_unit as unit', 'unit.id', '=', 'rtua.unit_id')
            ->where('rtua.college_id', $collegeId)
            ->where('rtua.unit_id', $subjectUnitId)
            ->where('rtua.student_id', $studentId)
            ->get(['rtua.*', 'unit.unit_code']);

        foreach ($unitAssessmentList as $row) {
            $date = ($row->due_date != '') ? date('d/m/Y', strtotime($row->due_date)) : '-';
            $time = ($row->due_time != '') ? date('H:i:s A', strtotime($row->due_time)) : '-';
            $row->dueDateTime = $date.' '.$time;
        }

        return $unitAssessmentList;
    }

    public function deleteUnitAssessmentInfo($unitAssessmentId)
    {
        return TraineeUnitAssessment::where('id', $unitAssessmentId)->delete();
    }

    public function assignTask2Module($collegeId, $dataArr, $userId)
    {

        $taskInfo = AssessmentTask::find($dataArr['taskID']);
        // print_r($dataArr);exit;
        // print_r($taskInfo);exit;

        //  $userId = Auth::user()->id;
        $objUnitAssessment = new TraineeUnitAssessment;

        $objUnitAssessment->college_id = $collegeId;
        $objUnitAssessment->student_id = $dataArr['studentID'];
        $objUnitAssessment->training_plan_id = $dataArr['trainingPlanID'];
        $objUnitAssessment->unit_id = $dataArr['subjectUnitID'];
        $objUnitAssessment->task_id = $dataArr['taskID'];

        $objUnitAssessment->task_name = ($taskInfo->task_name != '') ? $taskInfo->task_name : null;
        $objUnitAssessment->description = ($taskInfo->task_description != '') ? $taskInfo->task_description : null;

        $objUnitAssessment->due_date = ($dataArr['visitDate'] != '') ? date('Y-m-d', strtotime($dataArr['visitDate'])) : null;
        $objUnitAssessment->due_time = ($dataArr['visitTime']) ? $dataArr['visitTime'] : null;
        $objUnitAssessment->is_competency = (isset($dataArr['competency']) && $dataArr['competency'] != '') ? $dataArr['competency'] : '0';

        $objUnitAssessment->created_by = $userId;
        $objUnitAssessment->updated_by = $userId;

        $objUnitAssessment->save();

        return ['status' => 'true', 'action' => 'alert-success', 'msg' => 'Selected Task Assign Successfully.'];
    }
}
