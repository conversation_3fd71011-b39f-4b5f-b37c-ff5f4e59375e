<?php

namespace App\ProcessTask\Course;

use App\Classes\SiteConstants;
use App\Model\v2\SubjectUnits;
use App\Model\v2\Units;
use App\ProcessTask\Course\Payloads\AddUnitToCoursePayload;
use Closure;
use Exception;

class FindAndUpdateUnit
{
    public function handle(AddUnitToCoursePayload $payload, Closure $next): AddUnitToCoursePayload
    {
        /* get units data from the master units collection table */
        $unitData = $payload->unitToSave;
        $unit = SubjectUnits::where(['id' => $unitData->id, 'course_id' => $payload->course->id])->first();

        if (! $unit) {
            throw new Exception('Unit record not found.');
        }

        $coreAdded = $electiveAdded = 0;
        $maxCore = $payload->template->no_of_core_subject ?? 0;
        $maxElective = $payload->template->no_of_elective_subject ?? 0;
        if ($payload->template->templateunits) {
            $coreAdded = $payload->template->templateunits->where('unit_type', SiteConstants::CORETEXT)->where('unit_id', '!=', $unitData->id)->count();
            $electiveAdded = $payload->template->templateunits->where('unit_type', SiteConstants::ELECTIVETEXT)->where('unit_id', '!=', $unitData->id)->count();
        }

        $unitType = $unitData->unit_type ?? SiteConstants::ELECTIVETEXT;

        $saveData = [
            'unit_code' => $unitData->unit_code,
            'vet_unit_code' => $unitData->vet_unit_code,
            'unit_name' => $unitData->unit_name,
            'description' => $unitData->description,
            'unit_type' => $unitType,
            'field_education' => $unitData->field_education ?? '',
            'delivery_mode' => $unitData->delivery_mode ?? 'NNN',
            'internal' => $unitData->internal ?? 'N',
            'external' => $unitData->external ?? 'N',
            'workplace_based_delivery' => $unitData->workplace_based_delivery ?? 'N',
            'nominal_hours' => $unitData->nominal_hours ?? '',
            'tution_fees' => $unitData->tution_fees ?? '',
            'domestic_tution_fees' => $unitData->domestic_tution_fees ?? '',
            'module_unit_flag' => $unitData->module_unit_flag ?? '',
            'vet_flag' => $unitData->vet_flag ?? '',
            'work_placement' => $unitData->work_placement ?? '',
            'AVETMISS_Report' => $unitData->AVETMISS_Report ?? '',
            'status' => $unitData->status ?? '',
        ];

        if (! $unit->canBeUpdated($saveData)) {
            throw new Exception(config_locale(['messages.courses.cannotupdateuni']));
        }

        if ($unitType == SiteConstants::CORETEXT && $coreAdded >= $maxCore) {
            throw new Exception(config_locale(['messages.courses.coreexceeded', ['count' => $coreAdded]]));
        } elseif ($electiveAdded >= $maxElective) {
            throw new Exception(config_locale(['messages.courses.electiveexceeded', ['count' => $coreAdded]]));
        }

        $unit->update($saveData);

        $payload->subjectUnit = $unit;

        return $next($payload);

    }
}
