<?php

namespace App\Http\Requests;

use App\Model\v2\Courses;
use App\Model\v2\CourseTemplateStructure;
use App\Services\CourseSetupService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreUnitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /* prepare data before validating */
    protected function prepareForValidation()
    {
        $deliveryInternal = ($this->delivery_mode_internal === true) ? 'Y' : 'N';
        $deliveryExternal = ($this->delivery_mode_external === true) ? 'Y' : 'N';
        $deliveryWorkplace = ($this->delivery_mode_workplace === true) ? 'Y' : 'N';
        $deliveryMode = implode('', [$deliveryInternal, $deliveryExternal, $deliveryWorkplace]);
        $unit_type = (strtolower($this->unit_type) == 'core') ? 'Core' : 'Elective';
        $status = ($this->status) ? 1 : 0;
        // all units should be active for (status is only applicable for units from tga site)
        $status = 1;
        $vet_flag = ($this->vet_flag) ? 1 : 0;
        $AVETMISS_Report = ($this->AVETMISS_Report) ? 1 : 0;
        $work_placement = ($this->work_placement) ? 1 : 0;
        $AVETMISS_Report = ($this->AVETMISS_Report) ? 1 : 0;
        $tution_fees = strToNumber($this->tution_fees);
        $domestic_tution_fees = strToNumber($this->domestic_tution_fees);

        $nominal_hours = strToNumber($this->nominal_hours);

        $level = (int) $this->level ?? null;
        $is_long_indicator = ($this->is_long_indicator) ? 1 : 0;
        $is_assessment = ($this->is_assessment) ? 1 : 0;
        $is_active = $status = ($this->status) ? 1 : 0;
        $inc_in_certificate = ($this->inc_in_certificate) ? 1 : 0;
        $EFTSL_study_load = strToNumber($this->EFTSL_study_load);

        $this->merge([
            'college_id' => Auth::user()->college_id,
            'delivery_mode' => $deliveryMode,
            'internal' => $deliveryInternal,
            'external' => $deliveryExternal,
            'workplace_based_delivery' => $deliveryWorkplace,
            'unit_type' => $unit_type,
            'AVETMISS_Report' => $AVETMISS_Report,
            'vet_flag' => $vet_flag,
            'work_placement' => $work_placement,
            'status' => $status,
            'unit_code' => strtoupper($this->unit_code),
            'tution_fees' => $tution_fees ?? null,
            'domestic_tution_fees' => $domestic_tution_fees ?? null,
            'subject_code' => strtoupper($this->unit_code),
            'subject_name' => $this->unit_name ?? '',
            'subject_type' => $unit_type,
            'subject_fee' => $tution_fees ?? null,
            'domestic_subject_fee' => $domestic_tution_fees ?? null,
            'nominal_hours' => $nominal_hours ?? null,
            'contact_hours' => $nominal_hours ?? null,
            'level' => $level,
            'is_long_indicator' => $is_long_indicator,
            'is_assessment' => $is_assessment,
            'is_active' => $is_active,
            'inc_in_certificate' => $inc_in_certificate,
            'EFTSL_study_load' => $EFTSL_study_load,
        ]);
    }

    private function hasNewCourseSetup()
    {
        $courseSetupService = new CourseSetupService;

        return $courseSetupService->hasNewCourseSetup();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return $this->getRulesNewCourseSetup();
        // if($this->hasNewCourseSetup()){
        //     return $this->getRulesNewCourseSetup();
        // }else{
        //     return $this->getRulesOldCourseSetup();
        // }
    }

    private function getRulesNewCourseSetup()
    {
        return [
            'id' => [
                'nullable',
                'integer',
                Rule::exists('App\Model\v2\SubjectUnits', 'id'),
            ],
            'college_id' => ['required'],
            'course_id' => [
                'required',
                'integer',
                Rule::exists('App\Model\v2\Courses', 'id')->where(function ($query) {
                    $query->where('college_id', Auth::user()->college_id);
                }),
            ],
            'course_subject_id' => [
                'nullable',
                'integer',
                Rule::exists('App\Model\v2\CourseSubjects', 'id')->where(function ($query) {
                    $query->where('course_id', $this->course_id);
                }),
            ],
            'unit_type' => ['required'],
            'tution_fees' => ['nullable', 'numeric'],
            'domestic_tution_fees' => ['nullable', 'numeric'],
            'delivery_mode' => ['required'],
            'internal' => ['nullable'],
            'external' => ['nullable'],
            'workplace_based_delivery' => ['nullable'],
            'nominal_hours' => ['nullable', 'numeric'],
            'contact_hours' => ['nullable', 'numeric'],
            'AVETMISS_Report' => ['required'],
            'field_education' => ['required'],
            'unit_code' => [
                'required',
                'string',
                Rule::unique('App\Model\v2\SubjectUnits')->where(function ($query) {
                    $templateId = $this->template['id'] ?? null;
                    $templateUnits = CourseTemplateStructure::where('course_template_id', $templateId)->get()->pluck('unit_id');
                    $courseSubjects = Courses::GetAllCourseSubjects($this->course_id, 'id', true);
                    $courseSubjects = ! empty($courseSubjects) ? $courseSubjects->pluck('id') : [];
                    $courseId = $this->course_id;
                    $query->where('id', '<>', $this->id)
                        ->whereIn('id', $templateUnits)
                        ->where(function ($query) use ($courseSubjects, $courseId) {
                            $query->whereIn('course_subject_id', $courseSubjects)
                                ->orWhere('course_id', $courseId);
                        })
                        ->whereNull('deleted_at');
                }),
            ],
            'vet_unit_code' => ['nullable', 'string'],
            'unit_name' => ['required', 'string'],
            'description' => ['nullable', 'string'],
            'module_unit_flag' => ['nullable', 'string'],
            'vet_flag' => ['required'],
            'work_placement' => ['required'],
            'status' => ['required'],
            'template' => ['nullable', 'array'],
            /* fields for subjects */
            'subject_code' => ['required', 'string'],
            'subject_name' => ['required', 'string'],
            'subject_type' => ['required'],
            'level' => ['nullable'],
            'is_long_indicator' => ['nullable'],
            'is_assessment' => ['nullable'],
            'inc_in_certificate' => ['nullable'],
            'is_active' => ['nullable'],
            'subject_fee' => ['nullable', 'numeric'],
            'domestic_subject_fee' => ['nullable', 'numeric'],
            'course_stage' => ['nullable'],
            'credit_point' => ['nullable'],
            'discipline_broad_type' => ['nullable'],
            'discipline_narrow_sub_type' => ['nullable'],
            'discipline_narrow_type' => ['nullable'],
            'grading_type' => ['nullable', 'integer'],
            'max_marks_allow' => ['nullable'],
            'EFTSL_study_load' => ['nullable'],
        ];
    }

    private function getRulesOldCourseSetup()
    {
        return [
            'id' => [
                'nullable',
                'integer',
                Rule::exists('App\Model\v2\UnitModule', 'id')->where(function ($query) {
                    $query->where('college_id', Auth::user()->college_id);
                }),
            ],
            'college_id' => ['required'],
            'course_id' => [
                'required',
                'integer',
                Rule::exists('App\Model\v2\Courses', 'id')->where(function ($query) {
                    $query->where('college_id', Auth::user()->college_id);
                }),
            ],
            'subject_id' => [
                'nullable',
                'integer',
                Rule::exists('App\Model\v2\Subject', 'id')->where(function ($query) {
                    $query->where('college_id', Auth::user()->college_id);
                }),
            ],
            'unit_type' => ['required'],
            'solo_subject' => ['nullable'],
            'tution_fees' => ['nullable', 'numeric'],
            'delivery_mode' => ['required'],
            'delivery_mode_internal' => ['nullable'],
            'delivery_mode_external' => ['nullable'],
            'delivery_mode_workplace' => ['nullable'],
            'nominal_hours' => ['nullable', 'numeric'],
            'AVETMISS_Report' => ['required'],
            'field_education' => ['required'],
            'unit_code' => [
                'required',
                'string',
                Rule::unique('App\Model\v2\UnitModule')->where(function ($query) {
                    $courseSubjects = Courses::GetAllCourseSubjects($this->course_id, 'id')->pluck('id');
                    $courseId = $this->course_id;
                    $query->where('college_id', Auth::user()->college_id)
                        ->where('id', '<>', $this->id)
                        ->where(function ($query) use ($courseSubjects, $courseId) {
                            $query->whereIn('subject_id', $courseSubjects)
                                ->orWhere('course_id', $courseId);
                        });
                }),
            ],
            'vet_unit_code' => ['nullable', 'string'],
            'unit_name' => ['required', 'string'],
            'description' => ['nullable', 'string'],
            'module_unit_flag' => ['nullable', 'string'],
            'vet_flag' => ['required'],
            'work_placement' => ['required'],
            'status' => ['required'],
            'template' => ['nullable', 'array'],
        ];
    }
}
